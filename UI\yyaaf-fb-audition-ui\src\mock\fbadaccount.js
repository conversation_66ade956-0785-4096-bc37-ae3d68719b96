import Mock from 'mockjs'

// 生成FB广告账户列表数据
const generateFBAdAccounts = (count = 100) => {
  const accounts = []
  const currencies = ['USD', 'EUR', 'CNY', 'GBP', 'JPY']
  const statuses = ['active', 'paused', 'disabled']
  
  for (let i = 1; i <= count; i++) {
    const currency = Mock.Random.pick(currencies)
    const status = Mock.Random.pick(statuses)
    const balance = Mock.Random.float(0, 10000, 2, 2)
    
    accounts.push({
      id: i,
      account_id: `act_${Mock.Random.string('number', 12, 16)}`,
      name: `${Mock.Random.word(3, 8)} Ad Account ${i}`,
      status: status,
      currency: currency,
      balance: balance,
      spend_cap: Mock.Random.boolean() ? Mock.Random.float(1000, 50000, 2, 2) : null,
      created_time: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updated_time: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      timezone_name: Mock.Random.pick([
        'Asia/Shanghai',
        'America/New_York',
        'Europe/London',
        'Asia/Tokyo',
        'Europe/Paris'
      ]),
      timezone_offset_hours_utc: Mock.Random.pick([-8, -5, 0, 9, 1]),
      user_id: Mock.Random.integer(1, 50), // 关联到FB用户
      business_id: `${Mock.Random.string('number', 12, 16)}`,
      business_name: `${Mock.Random.word(2, 5)} Business`,
      account_status: Mock.Random.pick(['ACTIVE', 'DISABLED', 'UNSETTLED']),
      disable_reason: status === 'disabled' ? Mock.Random.pick([
        'RISK_PAYMENT',
        'RISK_ACCOUNT',
        'ADS_INTEGRITY_POLICY',
        'ADS_IP_REVIEW'
      ]) : null,
      age_hours: Mock.Random.integer(24, 8760), // 账户年龄（小时）
      amount_spent: Mock.Random.float(0, 100000, 2, 2),
      daily_spend_limit: Mock.Random.float(100, 5000, 2, 2),
      funding_source_details: {
        type: Mock.Random.pick(['CREDIT_CARD', 'PAYPAL', 'BANK_ACCOUNT']),
        display_string: Mock.Random.pick([
          '**** **** **** 1234',
          'PayPal Account',
          'Bank Account ****5678'
        ])
      }
    })
  }
  return accounts
}

// 模拟广告账户数据
let fbAdAccounts = generateFBAdAccounts()

// FB广告账户列表接口
Mock.mock(/\/api\/fbadaccount/, 'get', (options) => {
  const url = new URL(options.url, 'http://localhost')
  const page = parseInt(url.searchParams.get('page')) || 1
  const size = parseInt(url.searchParams.get('size')) || 20
  const userId = url.searchParams.get('userId')
  const accountName = url.searchParams.get('accountName') || ''
  const status = url.searchParams.get('status') || ''

  // 过滤数据
  let filteredAccounts = fbAdAccounts
  
  // 按用户ID过滤
  if (userId) {
    filteredAccounts = filteredAccounts.filter(account => 
      account.user_id === parseInt(userId)
    )
  }
  
  // 按账户名称过滤
  if (accountName) {
    filteredAccounts = filteredAccounts.filter(account => 
      account.name.toLowerCase().includes(accountName.toLowerCase())
    )
  }
  
  // 按状态过滤
  if (status) {
    filteredAccounts = filteredAccounts.filter(account => 
      account.status === status
    )
  }

  // 分页
  const start = (page - 1) * size
  const end = start + size
  const list = filteredAccounts.slice(start, end)

  return {
    code: 0,
    message: '获取成功',
    data: {
      list: list,
      total: filteredAccounts.length,
      page: page,
      size: size,
      pages: Math.ceil(filteredAccounts.length / size)
    }
  }
})

// FB广告账户详情接口
Mock.mock(/\/api\/fbadaccount\/(\d+)/, 'get', (options) => {
  const id = parseInt(options.url.match(/\/api\/fbadaccount\/(\d+)/)[1])
  const account = fbAdAccounts.find(a => a.id === id)
  
  if (account) {
    // 返回更详细的账户信息
    const detailedAccount = {
      ...account,
      insights: {
        impressions: Mock.Random.integer(10000, 1000000),
        clicks: Mock.Random.integer(100, 10000),
        spend: Mock.Random.float(100, 10000, 2, 2),
        cpm: Mock.Random.float(1, 50, 2, 2),
        cpc: Mock.Random.float(0.1, 5, 2, 2),
        ctr: Mock.Random.float(0.5, 10, 2, 2)
      },
      delivery_estimate: {
        daily_outcomes_curve: [
          { spend: 10, reach: 1000 },
          { spend: 50, reach: 4500 },
          { spend: 100, reach: 8000 },
          { spend: 200, reach: 15000 }
        ]
      },
      capabilities: Mock.Random.pick([
        ['CAN_CREATE_BRAND_LIFT_STUDY', 'CAN_USE_REACH_AND_FREQUENCY'],
        ['CAN_CREATE_BRAND_LIFT_STUDY'],
        ['CAN_USE_REACH_AND_FREQUENCY'],
        []
      ])
    }
    
    return {
      code: 0,
      message: '获取成功',
      data: detailedAccount
    }
  } else {
    return {
      code: 404,
      message: '广告账户不存在'
    }
  }
})

export default {
  fbAdAccounts
} 