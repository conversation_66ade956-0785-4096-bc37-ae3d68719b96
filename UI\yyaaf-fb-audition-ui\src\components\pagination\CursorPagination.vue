<template>
  <div class="cursor-pagination">
    <el-button 
      :disabled="!hasPrevious" 
      @click="handlePrevious"
    >
      <el-icon><ArrowLeft /></el-icon>
      Prev
    </el-button>
    <el-button 
      :disabled="!hasNext" 
      @click="handleNext"
    >
      <el-icon><ArrowRight /></el-icon>
      Next
    </el-button>
  </div>
</template>

<script>
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

export default {
  name: 'CursorPagination',
  components: {
    ArrowLeft,
    ArrowRight
  },
  props: {
    // 是否有下一页
    hasNext: {
      type: Boolean,
      default: false
    },
    // 是否有上一页
    hasPrevious: {
      type: Boolean,
      default: false
    },
    // 当前页的after游标
    after: {
      type: String,
      default: ''
    },
    // 当前页的before游标
    before: {
      type: String,
      default: ''
    }
  },
  methods: {
    // 处理上一页点击
    handlePrevious() {
      if (this.hasPrevious) {
        this.$emit('previous', {
          after: '',
          before: this.before
        })
      }
    },
    // 处理下一页点击
    handleNext() {
      if (this.hasNext) {
        this.$emit('next', {
          after: this.after,
          before: ''
        })
      }
    }
  }
}
</script>

<style scoped>
.cursor-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style> 