import Mock from 'mockjs'
import { getCampaignAnalytics, getCampaignDetailAnalytics } from '@/mock/campaignAnalytics'
import { getAdsetAnalytics, getAdsetDetailAnalytics } from '@/mock/adsetAnalytics'
import { getAdAnalytics, getAdDetailAnalytics } from '@/mock/adAnalytics'
// 引入FB用户和广告账户mock数据
import '@/mock/fbuser'
import '@/mock/fbadaccount'
import '@/mock/options'

// 设置响应延迟
Mock.setup({
  timeout: '200-600'
})

// 拦截空白对象请求，解决某些浏览器的预检请求问题
Mock.mock(/\/api\/?$/, 'options', () => {
  return ''
})

// 登录接口
Mock.mock(/\/api\/login/, 'post', (options) => {
  const body = JSON.parse(options.body || '{}')
  const { username, password } = body
  
  // 简单的登录验证
  if (username === 'admin' && password === 'admin123') {
    return {
      code: 0,
      message: '登录成功',
      data: {
        token: Mock.Random.string('lower', 32),
        userId: 1,
        username: 'admin',
        nickName: '管理员',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=admin&backgroundColor=409eff&textColor=ffffff'
      }
    }
  } else {
    return {
      code: 1,
      message: '用户名或密码错误'
    }
  }
})

// 获取用户信息接口
Mock.mock(/\/api\/login\/getInfo/, 'get', () => {
  return {
    code: 0,
    message: '获取成功',
    data: {
      userId: 1,
      username: 'admin',
      nickName: '管理员',
      avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=admin&backgroundColor=409eff&textColor=ffffff',
      roles: ['admin'],
      permissions: ['*:*:*']
    }
  }
})

// 获取用户全局授权状态接口
Mock.mock(/\/api\/login\/getUserOAthState/, 'get', () => {
  // 检查localStorage中的全局授权状态
  const globalAuthStatus = localStorage.getItem('fb_global_auth_status')
  const isAuthorized = globalAuthStatus === 'true'
  
  return {
    code: 0,
    message: '获取成功',
    data: isAuthorized
  }
})

// 登出接口
Mock.mock(/\/api\/logout/, 'post', () => {
  return {
    code: 0,
    message: '登出成功'
  }
})

// 仪表盘概览数据接口
Mock.mock(/\/api\/dashboard\/overview(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/dashboard/overview')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  console.log('Dashboard overview params:', params)
  
  // 可以根据queryDate生成不同的数据
  const queryDate = params.queryDate || new Date().toISOString().split('T')[0]
  
  const data = {
    impressions: Mock.Random.integer(10000, 50000),
    impressionsTrend: Mock.Random.float(-15, 25, 1, 1),
    clicks: Mock.Random.integer(500, 5000),
    clicksTrend: Mock.Random.float(-10, 30, 1, 1),
    conversions: Mock.Random.integer(50, 500),
    conversionsTrend: Mock.Random.float(-20, 40, 1, 1),
    todaySpend: Mock.Random.float(1000, 10000, 2, 2),
    todaySpendTrend: Mock.Random.float(-10, 20, 1, 1),
    date: queryDate
  }
  
  return {
    code: 0,
    message: 'success',
    data
  }
})

// 广告投放趋势数据接口
Mock.mock(/\/api\/dashboard\/trends(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/dashboard/trends')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  const timeRange = params.timeRange || 'month'
  const queryDate = params.queryDate || new Date().toISOString().split('T')[0]
  console.log('Dashboard trends params:', params)
  
  let xAxis = []
  let series = []
  
  if (timeRange === 'week') {
    xAxis = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    series = Array.from({ length: 7 }, () => Mock.Random.integer(50, 150))
  } else if (timeRange === 'month') {
    xAxis = Array.from({ length: 30 }, (_, i) => `${i + 1}日`)
    series = Array.from({ length: 30 }, () => Mock.Random.integer(20, 200))
  } else {
    xAxis = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    series = Array.from({ length: 12 }, () => Mock.Random.integer(100, 300))
  }
  
  return {
    code: 0,
    message: 'success',
    data: {
      xAxis,
      series,
      date: queryDate
    }
  }
})

// 广告类型分布数据接口
Mock.mock(/\/api\/dashboard\/ad-types(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/dashboard/ad-types')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  const queryDate = params.queryDate || new Date().toISOString().split('T')[0]
  console.log('Dashboard ad-types params:', params)
  
  const legendData = ['图片广告', '视频广告', '轮播广告', '动态广告', '链接广告']
  const seriesData = [
    { value: Mock.Random.integer(20, 40), name: '图片广告' },
    { value: Mock.Random.integer(15, 35), name: '视频广告' },
    { value: Mock.Random.integer(10, 25), name: '轮播广告' },
    { value: Mock.Random.integer(5, 20), name: '动态广告' },
    { value: Mock.Random.integer(5, 15), name: '链接广告' }
  ]
  
  return {
    code: 0,
    message: 'success',
    data: {
      legendData,
      seriesData,
      date: queryDate
    }
  }
})

// 仪表盘综合数据接口
Mock.mock(/\/api\/dashboard\/data(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/dashboard/data')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  const timeRange = params.timeRange || 'month'
  const queryDate = params.queryDate || new Date().toISOString().split('T')[0]
  console.log('Dashboard data params:', params)
  
  // 概览数据
  const overview = {
    impressions: Mock.Random.integer(10000, 50000),
    impressionsTrend: Mock.Random.float(-15, 25, 1, 1),
    clicks: Mock.Random.integer(500, 5000),
    clicksTrend: Mock.Random.float(-10, 30, 1, 1),
    conversions: Mock.Random.integer(50, 500),
    conversionsTrend: Mock.Random.float(-20, 40, 1, 1),
    todaySpend: Mock.Random.float(1000, 10000, 2, 2),
    todaySpendTrend: Mock.Random.float(-10, 20, 1, 1),
    date: queryDate
  }
  
  // 趋势数据
  let xAxis = []
  let series = []
  
  if (timeRange === 'week') {
    xAxis = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    series = Array.from({ length: 7 }, () => Mock.Random.integer(50, 150))
  } else if (timeRange === 'month') {
    xAxis = Array.from({ length: 30 }, (_, i) => `${i + 1}日`)
    series = Array.from({ length: 30 }, () => Mock.Random.integer(20, 200))
  } else {
    xAxis = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    series = Array.from({ length: 12 }, () => Mock.Random.integer(100, 300))
  }
  
  const trends = {
    xAxis,
    series,
    date: queryDate
  }
  
  // 广告类型分布
  const legendData = ['图片广告', '视频广告', '轮播广告', '动态广告', '链接广告']
  const seriesData = [
    { value: Mock.Random.integer(20, 40), name: '图片广告' },
    { value: Mock.Random.integer(15, 35), name: '视频广告' },
    { value: Mock.Random.integer(10, 25), name: '轮播广告' },
    { value: Mock.Random.integer(5, 20), name: '动态广告' },
    { value: Mock.Random.integer(5, 15), name: '链接广告' }
  ]
  
  const adTypes = {
    legendData,
    seriesData,
    date: queryDate
  }
  
  return {
    code: 0,
    message: 'success',
    data: {
      overview,
      trends,
      adTypes
    }
  }
})

// 广告系列分析 - 数据洞察接口
Mock.mock(/\/analytics\/campaign\/campaigninsights(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /analytics/campaign/campaigninsights')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  
  // 生成随机广告系列数据
  const items = Array.from({ length: 10 }, () => ({
    id: Mock.Random.guid(),
    name: Mock.Random.ctitle(5, 10) + '广告系列',
    objective: ['BRAND_AWARENESS', 'TRAFFIC', 'APP_INSTALLS', 'VIDEO_VIEWS', 'CONVERSIONS'][Mock.Random.integer(0, 4)],
    status: ['ACTIVE', 'PAUSED', 'ARCHIVED'][Mock.Random.integer(0, 2)],
    impressions: Mock.Random.integer(1000, 100000),
    clicks: Mock.Random.integer(100, 10000),
    ctr: Mock.Random.float(0.01, 0.2, 2, 2),
    conversions: Mock.Random.integer(10, 1000),
    conversionRate: Mock.Random.float(0.01, 0.15, 2, 2),
    spend: Mock.Random.float(100, 10000, 2, 2),
    cpc: Mock.Random.float(0.5, 5, 2, 2),
    cpm: Mock.Random.float(1, 10, 2, 2)
  }))

  return {
    code: 0,
    message: 'success',
    data: items,
    total: 100
  }
})

// 广告系列分析 - 概览数据接口
Mock.mock(/\/analytics\/campaign\/overview(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /analytics/campaign/overview')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  
  return {
    code: 0,
    message: 'success',
    data: {
      impressions: Mock.Random.integer(100000, 1000000),
      impressionsTrend: Mock.Random.float(-30, 30, 0, 2),
      clicks: Mock.Random.integer(10000, 100000),
      clicksTrend: Mock.Random.float(-30, 30, 0, 2),
      conversions: Mock.Random.integer(1000, 10000),
      conversionsTrend: Mock.Random.float(-30, 30, 0, 2),
      spend: Mock.Random.float(10000, 100000, 2, 2),
      spendTrend: Mock.Random.float(-30, 30, 0, 2)
    }
  }
})

// 广告系列分析 - 趋势数据接口
Mock.mock(/\/analytics\/campaign\/trends(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /analytics/campaign/trends')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  
  // 生成30天的数据
  const xAxis = Array.from({ length: 30 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (29 - i))
    return date.toISOString().split('T')[0]
  })
  
  const series = Array.from({ length: 30 }, () => {
    return Mock.Random.integer(1000, 10000)
  })

  return {
    code: 0,
    message: 'success',
    data: {
      xAxis,
      series
    }
  }
})

// 广告系列分析 - 目标分布接口
Mock.mock(/\/analytics\/campaign\/objective(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /analytics/campaign/objective')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  
  const data = [
    { name: 'BRAND_AWARENESS', value: Mock.Random.integer(20, 100) },
    { name: 'TRAFFIC', value: Mock.Random.integer(20, 100) },
    { name: 'APP_INSTALLS', value: Mock.Random.integer(20, 100) },
    { name: 'VIDEO_VIEWS', value: Mock.Random.integer(20, 100) },
    { name: 'CONVERSIONS', value: Mock.Random.integer(20, 100) }
  ]

  return {
    code: 0,
    message: 'success',
    data
  }
})

// 广告系列分析 - 状态分布接口
Mock.mock(/\/analytics\/campaign\/status(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /analytics/campaign/status')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  
  return {
    code: 0,
    message: 'success',
    data: {
      ACTIVE: Mock.Random.integer(50, 100),
      PAUSED: Mock.Random.integer(20, 50),
      ARCHIVED: Mock.Random.integer(10, 30)
    }
  }
})

// 广告系列详情分析接口
Mock.mock(/\/api\/campaigns\/(\w+)\/stats(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/campaigns/:id/stats')
  const url = new URL(options.url, window.location.origin)
  const id = options.url.match(/\/api\/campaigns\/(\w+)\/stats/)[1]
  const params = Object.fromEntries(url.searchParams.entries())
  const result = getCampaignDetailAnalytics(id, {
    startDate: params.startDate,
    endDate: params.endDate
  })
  
  return {
    code: 200,
    message: 'success',
    data: result
  }
})

// 广告组分析接口
Mock.mock(/\/api\/adsets\/stats(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/adsets/stats')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  const result = getAdsetAnalytics({
    page: parseInt(params.page) || 1,
    pageSize: parseInt(params.pageSize) || 10,
    startDate: params.startDate,
    endDate: params.endDate,
    campaignId: params.campaignId,
    adsetIds: params.adsetIds?.split(','),
    status: params.status
  })
  
  return {
    code: 200,
    message: 'success',
    data: result
  }
})

// 广告组详情分析接口
Mock.mock(/\/api\/adsets\/(\w+)\/stats(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/adsets/:id/stats')
  const url = new URL(options.url, window.location.origin)
  const id = options.url.match(/\/api\/adsets\/(\w+)\/stats/)[1]
  const params = Object.fromEntries(url.searchParams.entries())
  const result = getAdsetDetailAnalytics(id, {
    startDate: params.startDate,
    endDate: params.endDate
  })
  
  return {
    code: 200,
    message: 'success',
    data: result
  }
})

// 广告分析接口
Mock.mock(/\/api\/ads\/stats(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/ads/stats')
  const url = new URL(options.url, window.location.origin)
  const params = Object.fromEntries(url.searchParams.entries())
  const result = getAdAnalytics({
    page: parseInt(params.page) || 1,
    pageSize: parseInt(params.pageSize) || 10,
    startDate: params.startDate,
    endDate: params.endDate,
    campaignId: params.campaignId,
    adsetId: params.adsetId,
    adId: params.adId,
    creativeType: params.creativeType
  })
  
  return {
    code: 200,
    message: 'success',
    data: result
  }
})

// 广告详情分析接口
Mock.mock(/\/api\/ads\/(\w+)\/stats(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/ads/:id/stats')
  const url = new URL(options.url, window.location.origin)
  const id = options.url.match(/\/api\/ads\/(\w+)\/stats/)[1]
  const params = Object.fromEntries(url.searchParams.entries())
  const result = getAdDetailAnalytics(id, {
    startDate: params.startDate,
    endDate: params.endDate
  })
  
  return {
    code: 200,
    message: 'success',
    data: result
  }
})

// 广告系列目标选项接口
Mock.mock('/api/campaigns/objectives', 'get', () => {
  console.log('Mock intercepted: /api/campaigns/objectives')
  return {
    code: 200,
    message: 'success',
    data: [
      { name: '品牌知名度', value: 'BRAND_AWARENESS' },
      { name: '流量', value: 'TRAFFIC' },
      { name: '应用安装', value: 'APP_INSTALLS' },
      { name: '视频播放', value: 'VIDEO_VIEWS' },
      { name: '转化', value: 'CONVERSIONS' }
    ]
  }
})

// 广告系列列表接口
Mock.mock(/\/api\/campaigns(\?.+)?$/, 'get', () => {
  console.log('Mock intercepted: /api/campaigns')
  
  // 生成随机广告系列数据
  const campaigns = Array.from({ length: 50 }, (_, i) => ({
    id: `campaign_${i + 1}`,
    name: `测试广告系列 ${i + 1}`,
    objective: ['BRAND_AWARENESS', 'TRAFFIC', 'APP_INSTALLS', 'VIDEO_VIEWS', 'CONVERSIONS'][Math.floor(Math.random() * 5)],
    status: ['ACTIVE', 'PAUSED', 'ARCHIVED'][Math.floor(Math.random() * 3)]
  }))
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: campaigns.slice(0, 10),
      total: campaigns.length,
      page: 1,
      pageSize: 10
    }
  }
})

// 广告组列表接口
Mock.mock(/\/api\/adsets(\?.+)?$/, 'get', () => {
  console.log('Mock intercepted: /api/adsets')
  
  // 生成随机广告组数据
  const adsets = Array.from({ length: 40 }, (_, i) => ({
    id: `adset_${i + 1}`,
    name: `测试广告组 ${i + 1}`,
    campaignId: `campaign_${Math.floor(Math.random() * 10) + 1}`,
    campaignName: `测试广告系列 ${Math.floor(Math.random() * 10) + 1}`,
    status: ['ACTIVE', 'PAUSED', 'ARCHIVED'][Math.floor(Math.random() * 3)]
  }))
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: adsets.slice(0, 10),
      total: adsets.length,
      page: 1,
      pageSize: 10
    }
  }
})

// 按广告系列获取广告组
Mock.mock(/\/api\/campaigns\/(\w+)\/adsets(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/campaigns/:id/adsets')
  const campaignId = options.url.match(/\/api\/campaigns\/(\w+)\/adsets/)[1]
  
  // 生成特定广告系列的广告组
  const adsets = Array.from({ length: 10 }, (_, i) => ({
    id: `adset_${i + 1}`,
    name: `${campaignId}的广告组 ${i + 1}`,
    campaignId,
    campaignName: `测试广告系列 ${campaignId.split('_')[1]}`,
    status: ['ACTIVE', 'PAUSED', 'ARCHIVED'][Math.floor(Math.random() * 3)]
  }))
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: adsets,
      total: adsets.length,
      page: 1,
      pageSize: 10
    }
  }
})

// 广告列表接口
Mock.mock(/\/api\/ads(\?.+)?$/, 'get', () => {
  console.log('Mock intercepted: /api/ads')
  
  // 生成随机广告数据
  const ads = Array.from({ length: 60 }, (_, i) => ({
    id: `ad_${i + 1}`,
    name: `测试广告 ${i + 1}`,
    adsetId: `adset_${Math.floor(Math.random() * 10) + 1}`,
    adsetName: `测试广告组 ${Math.floor(Math.random() * 10) + 1}`,
    campaignId: `campaign_${Math.floor(Math.random() * 5) + 1}`,
    campaignName: `测试广告系列 ${Math.floor(Math.random() * 5) + 1}`,
    status: ['ACTIVE', 'PAUSED', 'ARCHIVED'][Math.floor(Math.random() * 3)],
    creativeType: ['IMAGE', 'VIDEO', 'CAROUSEL'][Math.floor(Math.random() * 3)]
  }))
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: ads.slice(0, 10),
      total: ads.length,
      page: 1,
      pageSize: 10
    }
  }
})

// 按广告组获取广告
Mock.mock(/\/api\/adsets\/(\w+)\/ads(\?.+)?$/, 'get', (options) => {
  console.log('Mock intercepted: /api/adsets/:id/ads')
  const adsetId = options.url.match(/\/api\/adsets\/(\w+)\/ads/)[1]
  
  // 生成特定广告组的广告
  const ads = Array.from({ length: 8 }, (_, i) => ({
    id: `ad_${i + 1}`,
    name: `${adsetId}的广告 ${i + 1}`,
    adsetId,
    adsetName: `测试广告组 ${adsetId.split('_')[1]}`,
    campaignId: `campaign_${Math.floor(Math.random() * 5) + 1}`,
    campaignName: `测试广告系列 ${Math.floor(Math.random() * 5) + 1}`,
    status: ['ACTIVE', 'PAUSED', 'ARCHIVED'][Math.floor(Math.random() * 3)],
    creativeType: ['IMAGE', 'VIDEO', 'CAROUSEL'][Math.floor(Math.random() * 3)]
  }))
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: ads,
      total: ads.length,
      page: 1,
      pageSize: 10
    }
  }
})

console.log('Mock.js is initialized with rules for campaigns, adsets and ads API')
export default Mock 