import request from '@/utils/request'

/**
 * 获取图片媒体库列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getImageList(params) {
  return request({
    url: '/media/images',
    method: 'get',
    params
  })
}

/**
 * 删除图片
 * @param {string|number} id 图片ID
 * @returns {Promise}
 */
export function deleteImage(data) {
  return request({
    url: `/media/images/delete`,
    method: 'post',
    data
  })
}

/**
 * 获取视频媒体库列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getVideoList(params) {
  return request({
    url: '/media/videos',
    method: 'get',
    params
  })
}

/**
 * 删除视频
 * @param {string|number} id 视频ID
 * @returns {Promise}
 */
export function deleteVideo(data) {
  return request({
    url: `/media/videos/delete`,
    method: 'post',
    data
  })
}

/**
 * 添加视频标签
 * @param {string|number} id 视频ID
 * @param {Array} tags 标签数组
 * @returns {Promise}
 */
export function addVideoTags(id, tags) {
  return request({
    url: `/media/videos/${id}/tags`,
    method: 'post',
    data: { tags }
  })
}

/**
 * 上传图片
 * @param {FormData} formData 包含图片文件和元数据的表单数据
 * @returns {Promise}
 */
export function uploadImage(formData) {
  return request({
    url: '/media/images/upload',
    method: 'post',
    data: formData,
    timeout: 120000,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传视频
 * @param {FormData} formData 包含视频文件和元数据的表单数据
 * @returns {Promise}
 */
export function uploadVideo(formData) {
  return request({
    url: '/media/videos/upload',
    method: 'post',
    data: formData,
    timeout: 120000,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}