// 广告分析模拟数据

// 生成日期数组，最近30天
const generateDates = (days = 30) => {
  const dates = [];
  const today = new Date();
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split('T')[0]);
  }
  return dates;
};

// 生成随机趋势数据
const generateTrends = (days = 30, seed = 1000) => {
  const dates = generateDates(days);
  return dates.map((date, index) => {
    // 添加一些随机波动，但保持整体趋势
    const factor = 1 + (Math.random() * 0.3 - 0.1) + (index / days) * 0.5;
    return {
      date,
      impressions: Math.floor(seed * factor * (1 + Math.random() * 0.5)),
      clicks: Math.floor((seed * factor * (1 + Math.random() * 0.5)) / 20),
      conversions: Math.floor((seed * factor * (1 + Math.random() * 0.5)) / 200),
      spend: +(seed * factor * (1 + Math.random() * 0.5) / 100).toFixed(2),
      ctr: +(Math.random() * 0.05 + 0.01).toFixed(4),
      conversionRate: +(Math.random() * 0.03 + 0.005).toFixed(4),
      cpc: +(Math.random() * 2 + 0.5).toFixed(2),
      cpm: +(Math.random() * 30 + 10).toFixed(2),
      engagementRate: +(Math.random() * 0.08 + 0.03).toFixed(4),
    };
  });
};

// 生成广告列表
const generateAds = (count = 20) => {
  const creativeTypes = ['IMAGE', 'VIDEO', 'CAROUSEL'];
  
  return Array.from({ length: count }, (_, i) => {
    const impressions = Math.floor(5000 + Math.random() * 45000);
    const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01));
    const conversions = Math.floor(clicks * (Math.random() * 0.1 + 0.05));
    const spend = +(Math.random() * 2000 + 500).toFixed(2);
    const ctr = +(clicks / impressions).toFixed(4);
    const conversionRate = +(conversions / clicks).toFixed(4);
    const cpc = +(spend / clicks).toFixed(2);
    const cpm = +((spend / impressions) * 1000).toFixed(2);
    const creativeType = creativeTypes[Math.floor(Math.random() * creativeTypes.length)];
    
    return {
      id: `ad_${i + 1}`,
      name: `测试广告 ${i + 1}`,
      adsetId: `adset_${Math.floor(Math.random() * 5) + 1}`,
      adsetName: `测试广告组 ${Math.floor(Math.random() * 5) + 1}`,
      campaignId: `campaign_${Math.floor(Math.random() * 3) + 1}`,
      campaignName: `测试广告系列 ${Math.floor(Math.random() * 3) + 1}`,
      creativeType,
      headline: `令人惊叹的产品 ${i + 1}`,
      description: `这是一个非常棒的产品，能解决您的各种问题。立即了解更多！`,
      callToAction: '立即购买',
      creativeUrl: creativeType === 'IMAGE' 
                 ? `https://picsum.photos/id/${Math.floor(Math.random() * 100)}/500/300`
                 : creativeType === 'VIDEO'
                 ? 'https://www.w3schools.com/html/mov_bbb.mp4'
                 : null,
      carouselItems: creativeType === 'CAROUSEL' 
                   ? Array.from({ length: 3 }, (_, j) => ({
                       id: `item_${i}_${j}`,
                       imageUrl: `https://picsum.photos/id/${Math.floor(Math.random() * 100)}/500/300`,
                       headline: `轮播标题 ${j + 1}`,
                       description: `轮播描述 ${j + 1}`
                     }))
                   : null,
      impressions,
      clicks,
      ctr,
      conversions,
      conversionRate,
      spend,
      cpc,
      cpm,
      frequency: +(Math.random() * 2 + 1).toFixed(2),
      relevanceScore: Math.floor(Math.random() * 10) + 1,
      engagementRate: +(Math.random() * 0.08 + 0.03).toFixed(4),
      startDate: '2023-01-01',
      endDate: '2023-12-31',
    };
  });
};

// 生成广告表现对比数据
const generateAdComparison = (count = 10) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `ad_comp_${i + 1}`,
    name: `广告 ${i + 1}`,
    type: ['图片', '视频', '轮播'][Math.floor(Math.random() * 3)],
    impressions: Math.floor(5000 + Math.random() * 45000),
    clicks: Math.floor(1000 + Math.random() * 5000),
    ctr: +(Math.random() * 0.05 + 0.01).toFixed(4),
    conversions: Math.floor(50 + Math.random() * 450),
    conversionRate: +(Math.random() * 0.03 + 0.005).toFixed(4),
    engagementRate: +(Math.random() * 0.08 + 0.03).toFixed(4),
  }));
};

// 生成投放时段分析数据
const generateTimeDistribution = () => {
  const data = {};
  
  // 每天24小时的数据
  for (let day = 0; day < 7; day++) {
    data[day] = {};
    
    for (let hour = 0; hour < 24; hour++) {
      data[day][hour] = Math.floor(Math.random() * 500 + 50);
    }
  }
  
  return data;
};

// 生成时间对比数据
const generatePeriodComparison = () => {
  const currentPeriod = generateTrends(15, 1200);
  const previousPeriod = generateTrends(15, 1000);
  
  return {
    current: currentPeriod,
    previous: previousPeriod
  };
};

// 生成概览数据
const generateOverview = () => {
  const impressions = Math.floor(Math.random() * 900000 + 100000);
  const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01));
  const ctr = clicks / impressions;
  const spend = +(Math.random() * 50000 + 10000).toFixed(2);
  
  return {
    impressions,
    impressionsTrend: +(Math.random() * 40 - 15).toFixed(1),
    clicks,
    clicksTrend: +(Math.random() * 30 - 10).toFixed(1),
    ctr,
    ctrTrend: +(Math.random() * 25 - 10).toFixed(1),
    spend,
    spendTrend: +(Math.random() * 20 - 5).toFixed(1),
  };
};

// 获取广告分析数据
export function getAdAnalytics(params) {
  const { page = 1, pageSize = 10, campaignId, adsetId } = params;
  const allAds = generateAds(50);
  
  // 过滤
  let filteredAds = [...allAds];
  
  if (campaignId) {
    filteredAds = filteredAds.filter(a => a.campaignId === campaignId);
  }
  
  if (adsetId) {
    filteredAds = filteredAds.filter(a => a.adsetId === adsetId);
  }
  
  if (params.adId) {
    filteredAds = filteredAds.filter(a => a.id === params.adId);
  }
  
  if (params.creativeType) {
    filteredAds = filteredAds.filter(a => a.creativeType === params.creativeType);
  }
  
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  
  return {
    items: filteredAds.slice(start, end),
    total: filteredAds.length,
    page,
    pageSize,
    trends: generateTrends(),
    adComparison: generateAdComparison(),
    timeDistribution: generateTimeDistribution(),
    periodComparison: generatePeriodComparison(),
    overview: generateOverview(),
  };
}

// 获取广告详情分析数据
export function getAdDetailAnalytics(id, params) {
  const ad = generateAds(1)[0];
  ad.id = id;
  
  return {
    ad,
    trends: generateTrends(),
    demographics: [
      { age: '18-24', percentage: Math.floor(Math.random() * 20 + 5) },
      { age: '25-34', percentage: Math.floor(Math.random() * 30 + 20) },
      { age: '35-44', percentage: Math.floor(Math.random() * 25 + 15) },
      { age: '45-54', percentage: Math.floor(Math.random() * 15 + 5) },
      { age: '55+', percentage: Math.floor(Math.random() * 10 + 2) },
    ],
    genders: [
      { gender: 'male', percentage: Math.floor(Math.random() * 60 + 30) },
      { gender: 'female', percentage: Math.floor(Math.random() * 60 + 30) },
      { gender: 'unknown', percentage: Math.floor(Math.random() * 10) },
    ],
    placements: [
      { name: '信息流', value: Math.floor(Math.random() * 60 + 30) },
      { name: '故事', value: Math.floor(Math.random() * 30 + 10) },
      { name: '右侧栏', value: Math.floor(Math.random() * 20 + 5) },
      { name: 'Audience Network', value: Math.floor(Math.random() * 15 + 3) },
    ],
    devices: [
      { device: 'mobile', percentage: Math.floor(Math.random() * 70 + 20) },
      { device: 'desktop', percentage: Math.floor(Math.random() * 40 + 10) },
      { device: 'tablet', percentage: Math.floor(Math.random() * 20 + 5) },
    ],
    hourlyDistribution: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      impressions: Math.floor(Math.random() * 1000 + 100),
      clicks: Math.floor(Math.random() * 100 + 10),
    })),
    weekdayDistribution: Array.from({ length: 7 }, (_, i) => ({
      weekday: i,
      impressions: Math.floor(Math.random() * 5000 + 1000),
      clicks: Math.floor(Math.random() * 500 + 100),
    })),
  };
} 