<template>
  <div class="fb-ad-account-container">
    <!-- Page title and action bar -->
    <div class="page-header">
      <div class="header-left">
        <h2>FB Ad Account List</h2>
        <div class="user-info" v-if="currentUser">
          <el-tag type="info">User: {{ currentUser }}</el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          Back to User List
        </el-button>
        <el-button type="primary" @click="refreshList">
          <el-icon><Refresh /></el-icon>
          Refresh
        </el-button>
      </div>
    </div>

    <!-- Search bar -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="Account Name">
          <el-input
            v-model="searchForm.name"
            placeholder="Enter account name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="Account Status">
          <el-select
            v-model="searchForm.accountStatus"
            placeholder="Select status"
            clearable
            style="width: 150px;"
          >
            <el-option v-for="item in accountStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
          <el-button @click="resetSearch">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Ad account list table -->
    <div class="table-container">
      <TablePagination
        :table-data="accountList"
        :loading="loading"
        :total="pagination.total"
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="Account ID" min-width="150" />
        
        <el-table-column prop="name" label="Account Info" min-width="250">
          <template #default="{ row }">
            <div class="account-info">
              <el-avatar 
                :size="40" 
                :src="generateAccountAvatar(row.name)"
                class="account-avatar"
                shape="square"
              >
                <el-icon><CreditCard /></el-icon>
              </el-avatar>
              <div class="account-details">
                <div class="account-name">{{ row.name }}</div>
                <div class="account-id">FB ID: {{ row.actAdAccountId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="accountStatus" label="Status" min-width="120">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.accountStatus)"
            >
              {{ getStatusText(row.accountStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="currency" label="Currency" min-width="80" />
        
        <el-table-column prop="balance" label="Balance" min-width="120">
          <template #default="{ row }">
            <span :class="{ 'low-balance': row.balance < 100 }">
              {{ formatCurrency(row.balance, row.currency) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="spendCap" label="Spend Cap" min-width="120">
          <template #default="{ row }">
            {{ row.spendCap ? formatCurrency(row.spendCap, row.currency) : 'Unlimited' }}
          </template>
        </el-table-column>
        <el-table-column prop="amountSpent" label="Total Spent" min-width="120">
          <template #default="{ row }">
            {{ row.amountSpent ? formatCurrency(row.amountSpent, row.currency) : 'Unlimited' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="Creation Date" min-width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" min-width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(row)"
            >
              View Details
            </el-button>
          </template>
        </el-table-column>
      </TablePagination>
    </div>

    <!-- Account detail dialog -->
    <el-dialog
      v-model="detailDialogVisible"
      title="Ad Account Details"
      width="600px"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedAccount" class="account-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="Account ID">
            {{ selectedAccount.id }}
          </el-descriptions-item>
          <el-descriptions-item label="FB Account ID">
            {{ selectedAccount.account_id }}
          </el-descriptions-item>
          <el-descriptions-item label="Account Name">
            {{ selectedAccount.name }}
          </el-descriptions-item>
          <el-descriptions-item label="Status">
            <el-tag :type="getStatusType(selectedAccount.accountStatus)">
              {{ getStatusText(selectedAccount.accountStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Currency">
            {{ selectedAccount.currency }}
          </el-descriptions-item>
          <el-descriptions-item label="Balance">
            <span :class="{ 'low-balance': selectedAccount.balance < 100 }">
              {{ formatCurrency(selectedAccount.balance, selectedAccount.currency) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="Spend Cap">
            {{ selectedAccount.spendCap ? formatCurrency(selectedAccount.spendCap, selectedAccount.currency) : 'Unlimited' }}
          </el-descriptions-item>
          <el-descriptions-item label="Timezone">
            {{ selectedAccount.timezoneName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="Timezone Offset">
            {{ selectedAccount.timezoneOffsetHoursUtc || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="Creation Time" :span="2">
            {{ formatDate(selectedAccount.createdTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">Close</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getFBAdAccountList, getFBAdAccountDetail } from '@/api/fbadaccount'
import { ElMessage } from 'element-plus'
import { Refresh, CreditCard, ArrowLeft } from '@element-plus/icons-vue'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'FBAdAccountList',
  components: {
    Refresh,
    CreditCard,
    ArrowLeft,
    TablePagination
  },
  data() {
    return {
      loading: false,
      accountList: [],
      selectedAccounts: [],
      selectedAccount: null,
      detailDialogVisible: false,
      currentUser: '',
      userId: null,
      searchForm: {
        name: '',
        accountStatus: 0
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      accountStatusOptions: [
        {label: 'ALL', value: 0},
        {label: 'ACTIVE', value: 1},
        {label: 'DISABLED', value: 2},
        {label: 'UNSETTLED', value: 3},
        {label: 'PENDING_RISK_REVIEW', value: 7},
        {label: 'PENDING_SETTLEMENT', value: 8},
        {label: 'IN_GRACE_PERIOD', value: 9},
        {label: 'PENDING_CLOSURE', value: 100},
        {label: 'CLOSED', value: 101},
        {label: 'ANY_ACTIVE', value: 201},
        {label: 'ANY_CLOSED', value: 202}
      ],
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    // Initialize page
    initPage() {
      // Get user information from route parameters
      this.userId = this.$route.query.userId
      this.currentUser = this.$route.query.userName
      
      if (!this.userId) {
        ElMessage.warning('Missing user information, returning to user management page')
        setTimeout(() => {
          this.goBack()
        }, 2000)
        return
      }
      
      this.fetchAccountList()
    },

    // Fetch ad account list
    async fetchAccountList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          limit: this.pagination.size,
          FBUserId: this.userId,
          ...this.searchForm
        }
        
        const response = await getFBAdAccountList(params)
        
        if (response.code === 0) {
          this.accountList = response.data || []
          this.pagination.total = response.total || 0
        } else {
          ElMessage.error(response.message || 'Failed to fetch ad account list')
        }
      } catch (error) {
        console.error('Failed to fetch ad account list:', error)
        ElMessage.error('Failed to fetch ad account list')
      } finally {
        this.loading = false
      }
    },

    // Search
    handleSearch() {
      this.pagination.page = 1
      this.fetchAccountList()
    },

    // Reset search
    resetSearch() {
      this.searchForm = {
        name: '',
        accountStatus: 0
      }
      this.pagination.page = 1
      this.fetchAccountList()
    },

    // Refresh list
    refreshList() {
      this.fetchAccountList()
    },

    // Selection change
    handleSelectionChange(selection) {
      this.selectedAccounts = selection
    },

    // Page size change
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchAccountList()
    },

    // Current page change
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchAccountList()
    },

    // View details
    async viewDetail(account) {
      try {
        this.loading = true
        const response = await getFBAdAccountDetail(account.id)
        
        if (response.code === 0) {
          this.selectedAccount = response.data
          this.detailDialogVisible = true
        } else {
          ElMessage.error(response.message || 'Failed to fetch account details')
        }
      } catch (error) {
        console.error('Failed to fetch account details:', error)
        ElMessage.error('Failed to fetch account details')
      } finally {
        this.loading = false
      }
    },

    // Close detail dialog
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.selectedAccount = null
    },

    // Go back to user management page
    goBack() {
      this.$router.push({ name: 'FBUserList' })
    },

    // Generate account avatar
    generateAccountAvatar(name) {
      // Use DiceBear API to generate account avatar
      const seed = encodeURIComponent(name || 'account')
      return `https://api.dicebear.com/7.x/shapes/svg?seed=${seed}&backgroundColor=67c23a&scale=80`
    },

    // Get status type
    getStatusType(status) {
      const statusMap = {
        1: 'success',
        2: 'danger',
        3: 'info',
        7: 'warning',
        8: 'warning',
        9: 'warning',
        100: 'warning',
        101: 'danger',
        201: 'success',
        202: 'danger'
      }
      return statusMap[status] || 'warning'
    },

    // Get status text
    getStatusText(status) {
      return this.accountStatusOptions.find(item => item.value === status)?.label || status
    },

    // Format currency
    formatCurrency(amount, currency = 'USD') {
      if (amount === null || amount === undefined) return '-'
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
      }).format(amount)
    },

    // Format date
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.fb-ad-account-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .header-left {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .back-btn {
      align-self: flex-start;
      padding: 0;
      font-size: 14px;
      color: #409eff;
      
      &:hover {
        color: #66b1ff;
      }
    }
    
    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
    
    .user-info {
      margin-top: 5px;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  
  .el-form {
    margin: 0;
  }
}

.table-container {
  margin-bottom: 20px;
  
  .low-balance {
    color: #f56c6c;
    font-weight: 600;
  }
}

.account-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .account-avatar {
    flex-shrink: 0;
    background: linear-gradient(135deg, #67c23a, #85ce61);
  }
  
  .account-details {
    flex: 1;
    min-width: 0;
    
    .account-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .account-id {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.account-detail {
  .low-balance {
    color: #f56c6c;
    font-weight: 600;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fb-ad-account-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    
    .header-left {
      h2 {
        font-size: 20px;
      }
    }
  }
  
  .search-bar {
    .el-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;
        
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }
  
  .account-info {
    .account-avatar {
      width: 32px;
      height: 32px;
    }
    
    .account-details {
      .account-name {
        font-size: 14px;
      }
      
      .account-id {
        font-size: 11px;
      }
    }
  }
}
</style> 