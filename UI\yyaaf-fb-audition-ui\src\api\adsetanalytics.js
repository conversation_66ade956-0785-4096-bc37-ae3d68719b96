import request from '@/utils/request'

export function adsetinsights(params) {
    return request({
      url: '/analytics/adset/adsetinsights',
      method: 'get',
      params
    })
  }
  
  export function overview(params) {
    return request({
      url: '/analytics/adset/overview',
      method: 'get',
      params
    })
  }

  export function trends(params) {
    return request({
      url: '/analytics/adset/trends',
      method: 'get',
      params
    })
  }

  export function compare(params) {
    return request({
      url: '/analytics/adset/compare',
      method: 'get',
      params
    })
  }