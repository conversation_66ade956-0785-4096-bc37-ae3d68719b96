<template>
  <div class="media-selector-container">
    <!-- Media Type Tabs -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="Image" name="IMAGE">
        <div class="media-list-container" v-loading="mediaLoading">
          <el-empty v-if="mediaList.length === 0" description="No Images" />
          
          <div v-else class="media-grid">
            <div 
              v-for="item in mediaList" 
              :key="item.id" 
              class="media-item"
              :class="{ 'is-selected': selectedMediaId === item.id }"
              @click="selectMedia(item)"
            >
              <el-image 
                :src="item.url" 
                fit="cover" 
                class="media-preview"
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="media-info">
                <span class="media-name">{{ item.name }}</span>
              </div>
            </div>
          </div>
          
          <!-- Cursor Pagination Component -->
          <cursor-pagination
            :has-next="pagination.hasNext"
            :has-previous="pagination.hasPrevious"
            :after="pagination.after"
            :before="pagination.before"
            @next="handleNextPage"
            @previous="handlePreviousPage"
          />
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="Video" name="VIDEO">
        <div class="media-list-container" v-loading="mediaLoading">
          <el-empty v-if="mediaList.length === 0" description="No Videos" />
          
          <div v-else class="media-grid">
            <div 
              v-for="item in mediaList" 
              :key="item.id" 
              class="media-item"
              :class="{ 'is-selected': selectedMediaId === item.id }"
              @click="selectMedia(item)"
            >
              <div class="video-preview" :style="{ backgroundImage: `url(${item.picture})` }">
                <el-icon class="play-icon"><VideoPlay /></el-icon>
              </div>
              <div class="media-info">
                <span class="media-name">{{ item.title }}</span>
              </div>
            </div>
          </div>
          
          <!-- Cursor Pagination Component -->
          <cursor-pagination
            :has-next="pagination.hasNext"
            :has-previous="pagination.hasPrevious"
            :after="pagination.after"
            :before="pagination.before"
            @next="handleNextPage"
            @previous="handlePreviousPage"
            prev-text="Prev"
            next-text="Next"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- Action Buttons -->
    <div class="media-actions">
      <el-button @click="handleCancel">Cancel</el-button>
      <el-button 
        type="primary" 
        @click="confirmMediaSelection" 
        :disabled="!selectedMediaId"
      >
        Confirm Selection
      </el-button>
    </div>
  </div>
</template>

<script>
import { Picture, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getImageList, getVideoList } from '@/api/media'
import CursorPagination from '@/components/pagination/CursorPagination.vue'

export default {
  name: 'MediaSelector',
  components: {
    Picture,
    VideoPlay,
    CursorPagination
  },
  props: {
    initialType: {
      type: String,
      default: 'IMAGE',
      validator: (value) => ['IMAGE', 'VIDEO'].includes(value)
    }
  },
  data() {
    return {
      activeTab: this.initialType,
      mediaLoading: false,
      mediaList: [],
      selectedMediaId: null,
      selectedMedia: null,
      pagination: {
        after: '',
        before: '',
        hasNext: false,
        hasPrevious: false
      }
    }
  },
  methods: {
    // Switch tabs
    handleTabChange(tab) {
      this.selectedMediaId = null
      this.selectedMedia = null
      this.pagination.after = '' // Clear after parameter
      this.pagination.before = '' // Clear before parameter
      this.fetchMediaList()
    },
    
    // Get media list
    async fetchMediaList() {
      this.mediaLoading = true
      try {
        // Build query parameters
        const params = {
          limit: 24
        }
        
        // Add Facebook API pagination parameters
        if (this.pagination.after) {
          params.after = this.pagination.after
        }
        if (this.pagination.before) {
          params.before = this.pagination.before
        }
        
        // Call different APIs based on media type
        let res
        if (this.activeTab === 'IMAGE') {
          res = await getImageList(params)
        } else {
          res = await getVideoList(params)
        }
        
        // Process media data
        this.mediaList = res.data || []
        
        // Update pagination information
        this.pagination.after = res.paging?.cursors?.after || ''
        this.pagination.before = res.paging?.cursors?.before || ''
        this.pagination.hasNext = !!res.paging?.next
        this.pagination.hasPrevious = !!res.paging?.previous
        
      } catch (error) {
        console.error('Failed to get media list:', error)
        ElMessage.error('Failed to get media list')
      } finally {
        this.mediaLoading = false
      }
    },
    
    // Handle previous page
    handlePreviousPage(pagination) {
      this.pagination.after = pagination.after
      this.pagination.before = pagination.before
      this.fetchMediaList()
    },
    
    // Handle next page
    handleNextPage(pagination) {
      this.pagination.after = pagination.after
      this.pagination.before = pagination.before
      this.fetchMediaList()
    },
    
    // Select media
    selectMedia(media) {
      this.selectedMediaId = media.id
      this.selectedMedia = media
    },
    
    // Confirm media selection
    confirmMediaSelection() {
      if (!this.selectedMedia) {
        ElMessage.warning('Please select a media file')
        return
      }
      
      this.$emit('select', {
        media: this.selectedMedia,
        type: this.activeTab
      })
    },
    
    // Cancel selection
    handleCancel() {
      this.$emit('cancel')
    }
  },
  mounted() {
    this.fetchMediaList()
  }
}
</script>

<style scoped>
.media-selector-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.media-list-container {
  flex: 1;
  min-height: 400px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 10px;
}

.media-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.media-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.media-item.is-selected {
  border: 2px solid #409eff;
}

.media-preview {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.video-preview {
  width: 100%;
  height: 150px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.video-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}

.play-icon {
  color: white;
  font-size: 24px;
  z-index: 1;
}

.media-info {
  padding: 8px;
  background-color: #f5f7fa;
}

.media-name {
  font-size: 14px;
  color: #606266;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #909399;
  font-size: 20px;
}
</style> 