import request from '@/utils/request'

// 获取广告组列表
export function getAdsetList(params) {
  return request({
    url: '/adsets',
    method: 'get',
    params
  })
}

// 同步广告组
export function getSyncAdsets(params) {
  return request({
    url: '/adsets/sync',
    method: 'get',
    params
  })
}

// 获取广告组详情
export function getAdsetDetail(id) {
  return request({
    url: `/adsets/${id}`,
    method: 'get'
  })
}

// 创建广告组
export function createAdset(data) {
  return request({
    url: '/adsets',
    method: 'post',
    data
  })
}

// 更新广告组
export function updateAdset(id, data) {
  return request({
    url: `/adsets/update/${id}`,
    method: 'post',
    data
  })
}

// 删除广告组
export function deleteAdset(data) {
  return request({
    url: `/adsets/delete`,
    method: 'post',
    data
  })
}

// 修改广告组状态
export function updateAdsetStatus(id, status) {
  return request({
    url: `/adsets/status/${id}`,
    method: 'post',
    data: { status }
  })
}

// 获取广告组选项
export function getAdsetOptions(campaignId) {
  return request({
    url: '/adsets/options',
    method: 'get',
    params: { campaignId }
  })
}

// 获取广告组统计数据
export function getAdsetStats(params) {
  return request({
    url: `/adsets/stats`,
    method: 'get',
    params
  })
}

// 获取定位选项列表
export function getTargetingOptions(type) {
  return request({
    url: '/targeting-options',
    method: 'get',
    params: { type }
  })
}

// 获取定位建议
export function getTargetingSuggestions(query, type) {
  return request({
    url: '/targeting-suggestions',
    method: 'get',
    params: { query, type }
  })
} 