// 广告组分析模拟数据

// 生成日期数组，最近30天
const generateDates = (days = 30) => {
  const dates = [];
  const today = new Date();
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split('T')[0]);
  }
  return dates;
};

// 生成随机趋势数据
const generateTrends = (days = 30, seed = 1000) => {
  const dates = generateDates(days);
  return dates.map((date, index) => {
    // 添加一些随机波动，但保持整体趋势
    const factor = 1 + (Math.random() * 0.3 - 0.1) + (index / days) * 0.5;
    return {
      date,
      impressions: Math.floor(seed * factor * (1 + Math.random() * 0.5)),
      clicks: Math.floor((seed * factor * (1 + Math.random() * 0.5)) / 20),
      conversions: Math.floor((seed * factor * (1 + Math.random() * 0.5)) / 200),
      spend: +(seed * factor * (1 + Math.random() * 0.5) / 100).toFixed(2),
      ctr: +(Math.random() * 0.05 + 0.01).toFixed(4),
      conversionRate: +(Math.random() * 0.03 + 0.005).toFixed(4),
      cpc: +(Math.random() * 2 + 0.5).toFixed(2),
      cpm: +(Math.random() * 30 + 10).toFixed(2),
    };
  });
};

// 生成广告组列表
const generateAdsets = (count = 20) => {
  const statuses = ['ACTIVE', 'PAUSED', 'ARCHIVED'];
  
  return Array.from({ length: count }, (_, i) => {
    const impressions = Math.floor(10000 + Math.random() * 90000);
    const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01));
    const conversions = Math.floor(clicks * (Math.random() * 0.1 + 0.05));
    const spend = +(Math.random() * 5000 + 1000).toFixed(2);
    const ctr = +(clicks / impressions).toFixed(4);
    const conversionRate = +(conversions / clicks).toFixed(4);
    const cpc = +(spend / clicks).toFixed(2);
    const cpm = +((spend / impressions) * 1000).toFixed(2);
    
    return {
      id: `adset_${i + 1}`,
      name: `测试广告组 ${i + 1}`,
      campaignId: `campaign_${Math.floor(Math.random() * 5) + 1}`,
      campaignName: `测试广告系列 ${Math.floor(Math.random() * 5) + 1}`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      impressions,
      clicks,
      ctr,
      conversions,
      conversionRate,
      spend,
      cpc,
      cpm,
      frequency: +(Math.random() * 2 + 1).toFixed(2),
      reach: Math.floor(impressions / (Math.random() * 2 + 1)),
      startDate: '2023-01-01',
      endDate: '2023-12-31',
    };
  });
};

// 生成受众分布数据
const generateAudienceDistribution = () => {
  return [
    { name: '18-24岁', value: Math.floor(Math.random() * 30 + 10) },
    { name: '25-34岁', value: Math.floor(Math.random() * 40 + 20) },
    { name: '35-44岁', value: Math.floor(Math.random() * 30 + 15) },
    { name: '45-54岁', value: Math.floor(Math.random() * 20 + 10) },
    { name: '55岁以上', value: Math.floor(Math.random() * 15 + 5) },
  ];
};

// 生成平台分布数据
const generatePlatformDistribution = () => {
  return [
    { name: 'Facebook', value: Math.floor(Math.random() * 50 + 30) },
    { name: 'Instagram', value: Math.floor(Math.random() * 40 + 20) },
    { name: 'Audience Network', value: Math.floor(Math.random() * 20 + 10) },
    { name: 'Messenger', value: Math.floor(Math.random() * 15 + 5) },
  ];
};

// 生成概览数据
const generateOverview = () => {
  return {
    activeAdsets: Math.floor(Math.random() * 30 + 10),
    activeAdsetsTrend: +(Math.random() * 30 - 10).toFixed(1),
    impressions: Math.floor(Math.random() * 900000 + 100000),
    impressionsTrend: +(Math.random() * 40 - 15).toFixed(1),
    conversions: Math.floor(Math.random() * 9000 + 1000),
    conversionsTrend: +(Math.random() * 50 - 20).toFixed(1),
    spend: +(Math.random() * 50000 + 10000).toFixed(2),
    spendTrend: +(Math.random() * 25 - 5).toFixed(1),
  };
};

// 获取广告组分析数据
export function getAdsetAnalytics(params) {
  const { page = 1, pageSize = 10, campaignId } = params;
  const allAdsets = generateAdsets(50);
  
  // 过滤
  let filteredAdsets = [...allAdsets];
  
  if (campaignId) {
    filteredAdsets = filteredAdsets.filter(a => a.campaignId === campaignId);
  }
  
  if (params.adsetIds && params.adsetIds.length > 0) {
    filteredAdsets = filteredAdsets.filter(a => params.adsetIds.includes(a.id));
  }
  
  if (params.status) {
    filteredAdsets = filteredAdsets.filter(a => a.status === params.status);
  }
  
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  
  return {
    items: filteredAdsets.slice(start, end),
    total: filteredAdsets.length,
    page,
    pageSize,
    trends: generateTrends(),
    audienceDistribution: generateAudienceDistribution(),
    platformDistribution: generatePlatformDistribution(),
    adsetComparison: filteredAdsets.slice(0, 10),
    overview: generateOverview(),
  };
}

// 获取广告组详情分析数据
export function getAdsetDetailAnalytics(id, params) {
  const adset = generateAdsets(1)[0];
  adset.id = id;
  
  return {
    adset,
    trends: generateTrends(),
    audienceDistribution: generateAudienceDistribution(),
    platformDistribution: generatePlatformDistribution(),
    ageGenderDistribution: [
      { age: '18-24', male: Math.floor(Math.random() * 20 + 5), female: Math.floor(Math.random() * 20 + 5) },
      { age: '25-34', male: Math.floor(Math.random() * 30 + 10), female: Math.floor(Math.random() * 30 + 10) },
      { age: '35-44', male: Math.floor(Math.random() * 25 + 8), female: Math.floor(Math.random() * 25 + 8) },
      { age: '45-54', male: Math.floor(Math.random() * 15 + 5), female: Math.floor(Math.random() * 15 + 5) },
      { age: '55+', male: Math.floor(Math.random() * 10 + 2), female: Math.floor(Math.random() * 10 + 2) },
    ],
    locationDistribution: [
      { country: '中国', value: Math.floor(Math.random() * 70 + 20) },
      { country: '美国', value: Math.floor(Math.random() * 30 + 10) },
      { country: '日本', value: Math.floor(Math.random() * 20 + 5) },
      { country: '韩国', value: Math.floor(Math.random() * 15 + 3) },
      { country: '其他', value: Math.floor(Math.random() * 10 + 1) },
    ],
    deviceDistribution: [
      { device: 'mobile', value: Math.floor(Math.random() * 70 + 20) },
      { device: 'desktop', value: Math.floor(Math.random() * 40 + 10) },
      { device: 'tablet', value: Math.floor(Math.random() * 20 + 5) },
    ],
    hourlyDistribution: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      impressions: Math.floor(Math.random() * 1000 + 100),
      clicks: Math.floor(Math.random() * 100 + 10),
    })),
    weekdayDistribution: Array.from({ length: 7 }, (_, i) => ({
      weekday: i,
      impressions: Math.floor(Math.random() * 5000 + 1000),
      clicks: Math.floor(Math.random() * 500 + 100),
    })),
  };
} 