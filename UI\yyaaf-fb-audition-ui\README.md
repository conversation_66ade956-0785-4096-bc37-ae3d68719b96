# FB广告管理过审后台UI

这是一个基于Vue 3和Element Plus开发的FB广告管理过审后台UI系统，用于管理Facebook广告资源，包括广告系列、广告组、广告创意和广告。

## 功能特性

- 用户登录与权限管理
- FB用户管理（查看、删除FB用户）
- FB广告账户管理（查看广告账户列表和详情）
- FB公共主页管理（查看用户的公共主页列表和详情）
- 广告系列管理
- 广告组管理
- 广告创意管理（图片和视频）
- 广告管理
- 素材库（图片和视频）
- 数据统计和分析
- 响应式布局，支持多设备访问

## 技术栈

- Vue 3 - 前端框架
- Element Plus - UI组件库
- Vue Router - 路由管理
- Pinia - 状态管理
- Axios - HTTP请求
- ECharts - 数据可视化
- SCSS - 样式处理

## 项目结构

```
src/
├── api/              # API接口
│   ├── fbuser.js     # FB用户相关接口
│   ├── fbadaccount.js # FB广告账户相关接口
│   └── fbpage.js     # FB公共主页相关接口
├── assets/           # 静态资源
├── components/       # 公共组件
│   ├── layout/       # 布局组件
│   └── upload/       # 上传组件
├── router/           # 路由配置
├── store/            # 状态管理
├── styles/           # 全局样式
├── utils/            # 工具函数
└── views/            # 页面组件
    ├── dashboard/    # 仪表盘
    ├── fbuser/       # FB用户管理
    ├── fbadaccount/  # FB广告账户管理
    ├── campaigns/    # 广告系列
    ├── adsets/       # 广告组
    ├── creatives/    # 广告创意
    ├── ads/          # 广告
    ├── media/        # 素材库
    ├── settings/     # 系统设置
    ├── login/        # 登录页面
    └── error/        # 错误页面
```

## 安装和运行

### 前提条件

- Node.js 14.x 或更高版本
- npm 7.x 或更高版本

### 安装依赖

```bash
npm install
```

### 开发模式运行

```bash
npm run serve
```

### 生产环境构建

```bash
npm run build
```

## 部署

将构建产物`dist`目录部署到Web服务器即可。

## 浏览器兼容性

- Chrome
- Firefox
- Safari
- Edge

## 国际化

项目支持中文和英文界面，可以通过系统设置进行切换。

## 新增功能说明

### FB用户管理

**功能描述：**
- **新增：FB平台全局授权**：首次使用需要进行FB平台授权才能获取用户数据
- **新增：Vuex状态管理**：使用store统一管理全局授权状态，确保状态一致性
- 查看FB用户列表，支持分页和搜索
- **用户授权管理**：支持FB用户授权、重新授权功能
- 删除FB用户（带确认提示）
- 从用户列表直接跳转到对应的广告账户管理
- **新增：用户头像显示**，支持自动生成头像或显示自定义头像
- **新增：使用TablePagination组件**，提供统一的表格分页体验

**使用方法：**
1. 在左侧导航栏点击"FB用户管理"
2. **首次使用**：
   - 页面会显示"请先进行FB平台授权以获取用户数据"的提示
   - 点击"FB平台授权"或"立即授权"按钮进行全局授权
   - 授权完成后会自动获取用户数据并显示用户列表
   - 授权状态通过Vuex store统一管理，确保应用状态一致
3. **已授权后**，在用户列表中可以：
   - 查看用户头像和基本信息（用户名）
   - 查看用户授权状态（已授权/未授权/授权过期）
   - 查看授权过期时间和最后更新时间
   - 使用搜索框按用户名搜索
   - 点击"授权"或"重新授权"按钮进行FB授权
   - 点击"广告账户"按钮查看该用户的广告账户
   - 点击"公共主页"按钮查看该用户的公共主页
   - 点击"删除"按钮删除用户（需确认）
   - 使用分页控件浏览更多用户

**授权功能说明：**
- **FB平台全局授权**：获取访问FB平台的基础权限，用于获取用户数据
- **状态管理**：通过Vuex store的`userOAthState`统一管理授权状态
- **已授权**（绿色标签）：用户已完成FB授权，可以正常使用
- **未授权**（红色标签）：用户尚未进行FB授权，显示"授权"按钮
- **授权过期**（橙色标签）：用户授权已过期，显示"重新授权"按钮
- 点击授权按钮会打开FB授权页面，完成授权后自动更新状态

**API接口：**
- `GET /api/fbuser` - 获取FB用户列表
- `GET /api/fbuser/{id}` - 获取FB用户详情
- `POST /api/fbuser/oauth` - 获取FB用户授权URL（支持全局授权和单用户授权）
- `GET /api/login/getUserOAthState` - 获取用户全局授权状态
- `POST /api/fbuser/delete/{id}` - 删除FB用户

**技术实现：**
- 使用Vuex store管理全局授权状态
- 通过`mapGetters`获取`userOAthState`状态
- 授权完成后自动调用`store.dispatch('getUserOAthState')`更新状态
- 支持localStorage作为状态持久化的备用方案

### FB广告账户管理

**功能描述：**
- 查看特定用户的广告账户列表
- 支持按账户名称和状态筛选
- 查看广告账户详细信息
- 显示账户余额、消费上限等关键信息
- **新增：账户图标显示**，为每个广告账户显示独特的图标
- **新增：使用TablePagination组件**，提供统一的表格分页体验

**使用方法：**
1. 从FB用户管理页面点击"查看广告账户"进入
2. 在广告账户列表中可以：
   - 查看账户图标和基本信息（账户名称、FB账户ID）
   - 使用搜索框按账户名称搜索
   - 使用状态下拉框筛选账户状态
   - 点击"查看详情"查看账户详细信息
   - 点击"返回用户管理"返回上一页

**API接口：**
- `GET /api/fbadaccount` - 获取FB广告账户列表
- `GET /api/fbadaccount/{id}` - 获取FB广告账户详情

**特色功能：**
- 余额低于100时会用红色高亮显示
- 支持多种货币格式显示
- 响应式设计，支持移动端访问
- 实时状态显示（活跃/暂停/禁用）
- **头像和图标自动生成**，使用DiceBear API生成美观的头像和图标

### FB公共主页管理

**功能描述：**
- 查看特定用户的FB公共主页列表
- 支持按主页名称和分类筛选
- 查看公共主页详细信息
- 显示主页分类、创建时间和更新时间
- **主页图标显示**，为每个公共主页显示独特的方形图标
- **使用TablePagination组件**，提供统一的表格分页体验
- **面包屑导航**，方便用户返回上级页面

**使用方法：**
1. 从FB用户管理页面点击"公共主页"进入
2. 在公共主页列表中可以：
   - 查看主页图标和基本信息（主页名称、主页ID）
   - 查看主页分类标签（商业、娱乐、品牌、媒体、其他）
   - 查看创建时间和最后更新时间
   - 使用搜索框按主页名称搜索
   - 使用分类下拉框筛选主页分类
   - 点击"查看详情"查看主页详细信息
   - 点击"返回用户列表"返回FB用户管理页面

**主页分类说明：**
- **商业**（蓝色标签）：企业、公司相关主页
- **娱乐**（绿色标签）：娱乐、音乐、影视相关主页
- **品牌**（橙色标签）：品牌、时尚、生活相关主页
- **媒体**（灰色标签）：新闻、媒体、传播相关主页
- **其他**（默认标签）：其他类型主页

**API接口：**
- `GET /api/fbpage` - 获取FB公共主页列表（需要userId参数）
- `GET /api/fbpage/{id}` - 获取FB公共主页详情

**特色功能：**
- 响应式设计，支持移动端访问
- 分类标签颜色区分，方便识别
- 主页图标自动生成，使用DiceBear API生成美观的方形图标
- 详情弹窗显示完整的主页信息
- 面包屑导航显示当前位置
- 支持批量操作（选择功能）

**Mock数据说明：**
- 为50个用户各生成5-15个公共主页
- 支持5种主页分类，名称根据分类自动生成
- 包含创建时间、更新时间等完整字段
- 总共生成约500个公共主页用于测试

### 技术实现要点

1. **组件化设计**：每个页面都是独立的Vue组件，便于维护和复用
2. **TablePagination组件**：统一的表格分页组件，提供一致的用户体验
3. **头像系统**：集成DiceBear API，自动生成用户头像和账户图标
4. **响应式布局**：使用Element Plus组件库，支持多设备访问
5. **错误处理**：完善的错误提示和异常处理机制
6. **用户体验**：加载状态、确认对话框、友好的提示信息
7. **数据格式化**：货币、日期等数据的本地化显示
8. **路由管理**：使用Vue Router进行页面跳转和参数传递

### 组件说明

#### TablePagination组件
位置：`src/components/table/TablePagination.vue`

**功能特性：**
- 集成表格和分页功能
- 支持加载状态显示
- 支持选择行功能
- 可配置分页大小选项
- 响应式事件处理

**使用示例：**
```vue
<TablePagination
  :table-data="dataList"
  :loading="loading"
  :total="pagination.total"
  v-model:current-page="pagination.page"
  v-model:page-size="pagination.size"
  :page-sizes="[10, 20, 50, 100]"
  @selection-change="handleSelectionChange"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
>
  <el-table-column prop="name" label="名称" />
  <!-- 更多列定义 -->
</TablePagination>
```

#### 头像系统
- **用户头像**：使用DiceBear Initials风格，显示用户姓名首字母
- **账户图标**：使用DiceBear Shapes风格，为每个账户生成独特图标
- **自动回退**：当头像加载失败时，自动显示首字母或默认图标
- **响应式尺寸**：在移动端自动调整头像大小

### 页面导航流程

```
FB用户管理页面 → 点击"查看广告账户" → FB广告账户列表页面
                                    ↓
                              点击"返回用户管理"
                                    ↓
                              返回FB用户管理页面
```

### 开发环境测试

项目已配置Mock数据，可以在开发环境中直接测试所有功能：
- FB用户：自动生成50个测试用户
- FB广告账户：自动生成100个测试账户，随机分配给用户
- 支持搜索、分页、删除等所有功能的模拟

### 技术实现要点

1. **组件化设计**：每个页面都是独立的Vue组件，便于维护和复用
2. **响应式布局**：使用Element Plus组件库，支持多设备访问
3. **错误处理**：完善的错误提示和异常处理机制
4. **用户体验**：加载状态、确认对话框、友好的提示信息
5. **数据格式化**：货币、日期等数据的本地化显示
6. **路由管理**：使用Vue Router进行页面跳转和参数传递

## 全局FB选项功能

### 功能描述
系统新增了全局FB用户和广告账户选择功能，在导航栏提供统一的选项切换，并将选中的值作为基础参数携带到所有API请求中。

### 主要特性
1. **导航栏选择器**: 在顶部导航栏添加了美观的FB用户和广告账户选择框
2. **全局状态管理**: 使用Vuex store管理选中的fbUserId和fbAdAccountId
3. **自动参数携带**: 在request.js请求拦截器中自动添加选中的参数到所有请求
4. **联动选择**: 选择FB用户后自动加载对应的广告账户列表
5. **状态显示**: 选择框中显示用户授权状态和账户状态标签
6. **响应式设计**: 适配不同屏幕尺寸，小屏幕下隐藏选择器

### 选择器说明
- **FB用户选择器**: 
  - 显示用户头像、姓名和授权状态
  - 支持搜索过滤
  - 显示授权状态标签（已授权/过期/未授权）
- **广告账户选择器**: 
  - 依赖于选中的FB用户
  - 显示账户名称和状态
  - 支持搜索过滤
  - 显示账户状态标签（ACTIVE/PAUSED/DISABLED）

### API参数携带
选中的fbUserId和fbAdAccountId会自动添加到所有API请求中：
- GET请求：添加到URL参数
- POST/PUT/DELETE请求：添加到请求体

### 使用说明
1. 登录后在导航栏可以看到两个选择框
2. 首先选择FB用户，选择后会自动加载对应的广告账户
3. 然后选择具体的广告账户
4. 选择完成后，所有后续的API请求都会携带这些参数
5. 可以随时切换选择，系统会实时更新全局状态
6. **状态持久化**：选择的FB用户和广告账户会保存到localStorage，刷新页面后自动恢复

### 技术实现要点
1. **模块化Vuex状态管理**: 将状态分离为auth和fbOptions两个独立模块
2. **组件化设计**: FB选项选择器提取为独立组件，便于复用和维护
3. **请求拦截器**: 在axios请求拦截器中自动添加选中的参数
4. **联动加载**: 选择用户后自动筛选对应的广告账户
5. **状态持久化**: 使用localStorage保存选择状态，刷新页面后自动恢复
6. **响应式同步**: 使用Vue的watch API确保UI和store状态保持一致
7. **Mock数据**: 提供完整的测试数据支持开发调试

### 项目架构

#### Store模块化结构
```
src/store/
├── index.js                 # 主store入口
├── getters.js               # 统一的getter文件
└── modules/
    ├── index.js             # 模块统一导出
    ├── auth.js              # 用户认证模块
    └── fbOptions.js         # FB选项模块
```

#### 组件结构
```
src/components/layout/
├── Layout.vue               # 主布局组件
├── SidebarItem.vue          # 侧边栏项目
├── Breadcrumb.vue           # 面包屑导航
└── FBOptionsSelector.vue    # FB选项选择器组件
```

**数据访问方式：**
- 使用统一getter：`store.getters.user`、`store.getters.fbUserId`
- 直接访问state：`store.state.auth.user`、`store.state.fbOptions.fbUserId`

**推荐使用getter访问数据，提供更好的封装和缓存优化**

### API接口
- `GET /api/options/getFBUserOptions` - 获取FB用户选项列表
- `GET /api/options/getFBAdAccountOptions` - 获取FB广告账户选项列表
- `GET /api/options/getFBCountryOptions` - 获取国家选项列表 