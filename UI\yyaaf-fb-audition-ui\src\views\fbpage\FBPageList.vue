<template>
  <div class="fb-page-container">
    <!-- Page title and action bar -->
    <div class="page-header">
      <div class="header-left">
        <h2>FB Page List</h2>
        <div class="user-info" v-if="userName">
          <el-tag type="info">User: {{ userName }}</el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          Back to User List
        </el-button>
        <el-button type="primary" @click="refreshList">
          <el-icon><Refresh /></el-icon>
          Refresh
        </el-button>
      </div>
    </div>

    <!-- Search bar -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="Page Name">
          <el-input
            v-model="searchForm.name"
            placeholder="Enter page name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
          <el-button @click="resetSearch">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Page list table -->
    <div class="table-container">
      <TablePagination
        :table-data="pageList"
        :loading="loading"
        :total="pagination.total"
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="Page ID" min-width="150" />
        
        <el-table-column prop="name" label="Page Info" min-width="200">
          <template #default="{ row }">
            <div class="page-info">
              <el-avatar 
                :size="40" 
                :src="row.avatar || generatePageAvatar(row.name)"
                shape="square"
                class="page-avatar"
              >
                {{ row.name ? row.name.charAt(0).toUpperCase() : 'P' }}
              </el-avatar>
              <div class="page-details">
                <div class="page-name">{{ row.name }}</div>
                <div class="page-id">ID: {{ row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="Category" align="center" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)" size="medium">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="createOn"
          label="Creation Date"
          align="center"
          min-width="150"
        >
          <template #default="{ row }">
            {{ formatDate(row.createOn) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="lastUpdateTime"
          label="Last Update Time"
          align="center"
          min-width="150"
        >
          <template #default="{ row }">
            {{ formatDate(row.lastUpdateTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" min-width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewPageDetail(row)"
            >
              View Details
            </el-button>
          </template>
        </el-table-column>
      </TablePagination>
    </div>
  </div>
</template>

<script>
import { getFBPageList, getFBPageDetail } from '@/api/fbpage'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, ArrowLeft } from '@element-plus/icons-vue'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'FBPageList',
  components: {
    Refresh,
    ArrowLeft,
    TablePagination
  },
  data() {
    return {
      loading: false,
      pageList: [],
      selectedPages: [],
      searchForm: {
        name: '',
        category: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      userId: '', // User ID
      userName: '' // User Name
    }
  },
  mounted() {
    this.initPageData()
    this.fetchPageList()
  },
  methods: {
    // Initialize page data
    initPageData() {
      this.userId = this.$route.query.userId || ''
      this.userName = this.$route.query.userName || ''
      
      if (!this.userId) {
        ElMessage.error('Missing user ID parameter')
        this.goBack()
        return
      }
    },

    // Fetch page list
    async fetchPageList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          limit: this.pagination.size,
          FBUserId: this.userId,
          ...this.searchForm
        }
        
        const response = await getFBPageList(params)
        
        if (response.code === 0) {
          this.pageList = response.data || []
          this.pagination.total = response.total || 0
        } else {
          ElMessage.error(response.message || 'Failed to fetch page list')
        }
      } catch (error) {
        console.error('Failed to fetch page list:', error)
        ElMessage.error('Failed to fetch page list')
      } finally {
        this.loading = false
      }
    },

    // Search
    handleSearch() {
      this.pagination.page = 1
      this.fetchPageList()
    },

    // Reset search
    resetSearch() {
      this.searchForm = {
        name: '',
        category: ''
      }
      this.pagination.page = 1
      this.fetchPageList()
    },

    // Refresh list
    refreshList() {
      this.fetchPageList()
    },

    // Go back to user list
    goBack() {
      this.$router.push('/fbuser/list')
    },

    // Selection change
    handleSelectionChange(selection) {
      this.selectedPages = selection
    },

    // Page size change
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchPageList()
    },

    // Current page change
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchPageList()
    },

    // View page details
    async viewPageDetail(page) {
      try {
        const response = await getFBPageDetail(page.id)
        
        if (response.code === 0) {
          const pageDetail = response.data
          
          ElMessageBox.alert(
            `
            <div style="text-align: left;">
              <p><strong>Page ID:</strong>${pageDetail.id}</p>
              <p><strong>Page Name:</strong>${pageDetail.name}</p>
              <p><strong>Category:</strong>${this.getCategoryLabel(pageDetail.category)}</p>
              <p><strong>Creation Date:</strong>${this.formatDate(pageDetail.createOn)}</p>
              <p><strong>Last Update:</strong>${this.formatDate(pageDetail.lastUpdateTime)}</p>
            </div>
            `,
            'Page Details',
            {
              confirmButtonText: 'OK',
              dangerouslyUseHTMLString: true
            }
          )
        } else {
          ElMessage.error(response.message || 'Failed to fetch page details')
        }
      } catch (error) {
        console.error('Failed to fetch page details:', error)
        ElMessage.error('Failed to fetch page details')
      }
    },

    // Generate page avatar URL
    generatePageAvatar(name) {
      const seed = encodeURIComponent(name || 'page')
      return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=67c23a&textColor=ffffff`
    },

    // Format date
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },

    // Get category tag type
    getCategoryTagType(category) {
      const typeMap = {
        'Business': 'primary',
        'Entertainment': 'success',
        'Brand': 'warning',
        'Media': 'info',
        'Other': 'default'
      }
      return typeMap[category] || 'default'
    },

    // Get category label
    getCategoryLabel(category) {
      const labelMap = {
        'Business': 'Business',
        'Entertainment': 'Entertainment',
        'Brand': 'Brand',
        'Media': 'Media',
        'Other': 'Other'
      }
      return labelMap[category] || category
    }
  }
}
</script>

<style lang="scss" scoped>
.fb-page-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    .user-info {
      margin-top: 5px;
    }
    
    .breadcrumb {
      :deep(.el-breadcrumb__inner) {
        color: #606266;
      }
      
      :deep(.el-breadcrumb__inner.is-link) {
        color: #409eff;
        cursor: pointer;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  
  .el-form {
    margin: 0;
  }
}

.table-container {
  margin-bottom: 20px;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .page-avatar {
    flex-shrink: 0;
  }
  
  .page-details {
    flex: 1;
    min-width: 0;
    
    .page-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .page-id {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fb-page-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    
    .header-left {
      h2 {
        font-size: 20px;
      }
    }
  }
  
  .search-bar {
    .el-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;
        
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }
  
  .page-info {
    .page-avatar {
      width: 32px;
      height: 32px;
    }
    
    .page-details {
      .page-name {
        font-size: 14px;
      }
      
      .page-id {
        font-size: 11px;
      }
    }
  }
  
  // 移动端操作按钮样式
  :deep(.el-table__fixed-right) {
    .el-button {
      margin: 2px;
      padding: 5px 8px;
      font-size: 12px;
      
      &.el-button--small {
        padding: 4px 6px;
      }
    }
  }
}
</style> 