<template>
  <div class="ad-form-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? 'Edit Ad' : 'Create Ad' }}</span>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            <span>Back</span>
          </el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="200px"
        label-position="right"
        :disabled="loading"
      >
        <!-- Basic Information -->
        <div class="form-section">
          <h3 class="section-title">Basic Information</h3>
          
          <el-form-item label="Ad Name" prop="name">
            <el-input v-model="formData.name" placeholder="Please enter ad name" />
          </el-form-item>
          
          <el-form-item label="Ad ID" v-if="isEdit">
            <el-input v-model="formData.id" disabled />
          </el-form-item>
          
          <el-form-item label="Ad Set" prop="adset_id">
            <el-select 
              v-model="formData.adset_id" 
              placeholder="Please select ad set" 
              filterable 
              style="width: 100%"
              :disabled="!!preselectedAdsetId"
            >
              <el-option 
                v-for="item in adsetOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="Status" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio-button label="ACTIVE">Active</el-radio-button>
              <el-radio-button label="PAUSED">Paused</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- Creative Selection -->
        <div class="form-section">
          <h3 class="section-title">Creative Selection</h3>
          
          <el-form-item label="Select from Creative Library">
            <el-button type="primary" @click="openCreativeSelector" :disabled="isEdit">
              Select from Creative Library
            </el-button>
            <div class="form-tip">Select an existing creative as the current ad creative</div>
          </el-form-item>
        </div>

        <!-- Creative Content -->
        <div class="form-section">
          <h3 class="section-title">Creative Content</h3>
          
          <div class="creative-content-container">
            <div class="creative-content-form">
              <el-form-item label="Creative Type" prop="adcreative_type">
                <el-radio-group v-model="formData.adcreative_type" :disabled="true">
                  <el-radio label="IMAGE">Image Ad</el-radio>
                  <el-radio label="VIDEO">Video Ad</el-radio>
                  <!--<el-radio label="CAROUSEL">Carousel Ad</el-radio>-->
                </el-radio-group>
              </el-form-item>
              
              <!-- Image Upload -->
              <template v-if="formData.adcreative_type === 'IMAGE'">
                <el-form-item label="Media Library">
                  <el-button type="primary" @click="openMediaLibrary('IMAGE')" :disabled="true">
                    Select from Media Library
                  </el-button>
                </el-form-item>
                
                <el-form-item label="Upload Image" prop="adcreative.imageUrl">
                  <el-upload
                    class="creative-upload"
                    :action="''"
                    :auto-upload="false"
                    :limit="1"
                    :on-change="handleImageChange"
                    :on-remove="handleImageRemove"
                    :file-list="imageFileList"
                    list-type="picture-card"
                    :disabled="true"
                  >
                    <el-icon v-if="!formData.imageUrl"><Plus /></el-icon>
                    <template #tip>
                      <div class="el-upload__tip">
                        Supports JPG, PNG formats, recommended size 1200x628 pixels
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>
              </template>
              
              <!-- Video Upload -->
              <template v-if="formData.adcreative_type === 'VIDEO'">
                <el-form-item label="Media Library">
                  <el-button type="primary" @click="openMediaLibrary('VIDEO')" :disabled="true">
                    Select Video from Media Library
                  </el-button>
                </el-form-item>
                
                <el-form-item label="Upload Video" prop="videoUrl">
                  <el-upload
                    class="creative-upload"
                    :action="''"
                    :auto-upload="false"
                    :limit="1"
                    :on-change="handleVideoChange"
                    :on-remove="handleVideoRemove"
                    :file-list="videoFileList"
                    :disabled="true"
                  >
                    <el-button>Select Video</el-button>
                    <template #tip>
                      <div class="el-upload__tip">
                        Supports MP4, WebM formats, recommended duration 15-30 seconds
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>
                
                <el-form-item label="Media Library">
                  <el-button type="primary" @click="openMediaLibrary('IMAGE')" :disabled="true">
                    Select Cover Image from Media Library
                  </el-button>
                </el-form-item>
                
                <el-form-item label="Upload Image" prop="adcreative.imageUrl">
                  <el-upload
                    class="creative-upload"
                    :action="''"
                    :auto-upload="false"
                    :limit="1"
                    :on-change="handleImageChange"
                    :on-remove="handleImageRemove"
                    :file-list="imageFileList"
                    list-type="picture-card"
                    :disabled="true"
                  >
                    <el-icon v-if="!formData.imageUrl"><Plus /></el-icon>
                    <template #tip>
                      <div class="el-upload__tip">
                        Supports JPG, PNG formats, recommended size 1200x628 pixels
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>
              </template>
              
              <!-- Carousel Upload -->
              <template v-if="formData.adcreative_type === 'CAROUSEL'">
                <el-form-item label="Upload Carousel Images" prop="carouselImages">
                  <el-upload
                    class="carousel-uploader"
                    :action="''"
                    :auto-upload="false"
                    list-type="picture-card"
                    :on-change="handleCarouselChange"
                    :on-remove="handleCarouselRemove"
                    :file-list="carouselFileList"
                    multiple
                    :disabled="isEdit"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                  <div class="form-tip">
                    Up to 10 images can be uploaded, recommended size: 1080 x 1080 pixels
                  </div>
                  <el-button type="primary" size="small" @click="openMediaLibrary('IMAGE')" :disabled="isEdit">
                    Select from Media Library
                  </el-button>
                </el-form-item>
              </template>
              
              <el-form-item label="Ad Title" prop="adcreative.title">
                <el-input 
                  v-model="formData.adcreative.title" 
                  placeholder="Please enter ad title" 
                  show-word-limit
                  :disabled="true"
                />
              </el-form-item>
              
              <el-form-item label="Ad Description" prop="adcreative.message">
                <el-input 
                  v-model="formData.adcreative.message" 
                  placeholder="Please enter ad description" 
                  type="textarea" 
                  show-word-limit
                  :rows="5"
                  :disabled="true"
                />
              </el-form-item>
              
              <el-form-item label="Call to Action" prop="adcreative.callToAction">
                <el-select 
                  v-model="formData.adcreative.callToAction" 
                  placeholder="Please select call to action" 
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option-group
                    v-for="group in ctaOptions"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-option-group>
                </el-select>
              </el-form-item>
            </div>
            
            <!-- Preview -->
            <div class="creative-preview">
              <div class="preview-header">
                <span>Creative Preview</span>
              </div>
              
              <div class="preview-container">
                <!-- Image Creative Preview -->
                <template v-if="formData.adcreative_type === 'IMAGE' && formData.adcreative.imageUrl">
                  <div class="preview-media">
                    <el-image :src="formData.adcreative.imageUrl" fit="contain" class="preview-image"></el-image>
                  </div>
                </template>
                
                <!-- Video Creative Preview -->
                <template v-if="formData.adcreative_type === 'VIDEO' && formData.adcreative.videoUrl">
                  <div class="preview-media">
                    <video :src="formData.adcreative.videoUrl" controls class="preview-video"></video>
                  </div>
                </template>
                
                <!-- Carousel Creative Preview -->
                <template v-if="formData.adcreative_type === 'CAROUSEL' && formData.carouselImages && formData.carouselImages.length > 0">
                  <div class="preview-media">
                    <el-carousel height="300px" indicator-position="outside">
                      <el-carousel-item v-for="(image, index) in formData.carouselImages" :key="index">
                        <el-image :src="image.url" fit="contain" class="preview-image"></el-image>
                      </el-carousel-item>
                    </el-carousel>
                  </div>
                </template>
                
                <div class="preview-content">
                  <h3 v-if="formData.adcreative.title">{{ formData.adcreative.title }}</h3>
                  <p v-if="formData.adcreative.message" class="preview-message">{{ formData.adcreative.message }}</p>
                  <p v-if="formData.adcreative.callToAction" class="preview-cta">
                    <el-tag type="primary">{{ getCtaLabel(formData.adcreative.callToAction) }}</el-tag>
                  </p>
                  <p v-if="formData.adcreative.linkUrl" class="preview-link">
                    <a :href="formData.adcreative.linkUrl" target="_blank">{{ formData.adcreative.linkUrl }}</a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Target URL -->
        <div class="form-section">
          <h3 class="section-title">Target URL</h3>
          
          <el-form-item label="Target Link" prop="adcreative.linkUrl">
            <el-input 
              v-model="formData.adcreative.linkUrl" 
              placeholder="Please enter the redirect link after ad click, e.g., https://example.com/product" 
              :disabled="true"
            />
          </el-form-item>
          
          <!--<el-form-item label="Tracking Parameters" prop="trackingParams">
            <el-input 
              v-model="formData.trackingParams" 
              placeholder="Optional, e.g., utm_source=facebook&utm_medium=cpc" 
              :disabled="isEdit"
            />
            <div class="form-tip">
              Tracking parameters added to the URL help you track the performance of different ads
            </div>
          </el-form-item>-->
        </div>
        
        <!-- Submit Buttons -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">{{ isEdit ? 'Save Changes' : 'Create Ad' }}</el-button>
          <el-button @click="resetForm">Reset</el-button>
          <el-button @click="goBack">Cancel</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- Media Selection Dialog -->
    <el-dialog
      v-model="mediaLibraryVisible"
      :title="mediaType === 'IMAGE' ? 'Select Image' : 'Select Video'"
      width="80%"
      destroy-on-close
    >
      <media-selector 
        :initial-type="mediaType"
        @select="handleMediaSelected"
        @cancel="mediaLibraryVisible = false"
      />
    </el-dialog>

    <!-- Creative Selection Dialog -->
    <el-dialog
      v-model="creativeSelectorVisible"
      title="Select Creative"
      width="80%"
      destroy-on-close
    >
      <creative-selector
        @select="handleCreativeSelected"
        @cancel="creativeSelectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdDetail, createAd, updateAd } from '@/api/ad'
import { getAdsetOptions } from '@/api/adset'
import { Back, Plus, VideoCamera } from '@element-plus/icons-vue'
import { uploadImage, uploadVideo } from '@/api/media'
import MediaSelector from '@/views/creatives/components/MediaSelector.vue'
import CreativeSelector from '@/views/creatives/components/CreativeSelector.vue'

export default {
  name: 'AdForm',
  components: {
    Back,
    Plus,
    //VideoCamera,
    MediaSelector,
    CreativeSelector
  },
  data() {
    return {
      loading: false,
      submitting: false,
      mediaLibraryVisible: false,
      creativeSelectorVisible: false,
      mediaType: 'IMAGE',
      formRef: null,
      carouselFileList: [],
      imageFileList: [],
      videoFileList: [],
      thumbnailFileList: [],
      adsetOptions: [],
      formData: {
        id: '',
        name: '',
        adset_id: '',
        status: 'PAUSED',
        adcreative_type: 'IMAGE',
        // Creative related fields
        trackingParams: '',
        creative_id: '',
        adcreative: {
          imageHash: '',
          imageUrl: '',
          videoId: '',
          videoUrl: '',
          carouselImages: [],
          title: '',
          message: '',
          callToAction: 'SHOP_NOW',
          linkUrl: ''
        },
      },
      rules: {
        name: [
          { required: true, message: 'Please enter ad name', trigger: 'blur' },
          //{ min: 2, max: 50, message: 'Length should be between 2 and 50 characters', trigger: 'blur' }
        ],
        adset_id: [
          { required: true, message: 'Please select ad set', trigger: 'change' }
        ],
        adcreative_type: [
          { required: true, message: 'Please select creative type', trigger: 'change' }
        ],
        imageUrl: [
          { required: true, message: 'Please upload creative material', trigger: 'change' }
        ],
        carouselImages: [
          { type: 'array', required: true, message: 'Please upload at least one carousel image', trigger: 'change' }
        ],
        title: [
          { required: true, message: 'Please enter ad title', trigger: 'blur' },
          //{ max: 40, message: 'Title can be up to 40 characters', trigger: 'blur' }
        ],
        message: [
          { required: true, message: 'Please enter ad description', trigger: 'blur' },
          //{ max: 125, message: 'Description can be up to 125 characters', trigger: 'blur' }
        ],
        callToAction: [
          { required: true, message: 'Please select call to action', trigger: 'change' }
        ],
        linkUrl: [
          { required: true, message: 'Please enter target link', trigger: 'blur' },
          { pattern: /^(http|https):\/\/[^ "']+$/, message: 'Please enter a valid URL', trigger: 'blur' }
        ]
      },
      ctaOptions: [
        {
          label: 'Shopping',
          options: [
            { value: 'SHOP_NOW', label: 'Shop Now' },
            { value: 'BUY_NOW', label: 'Buy Now' },
            { value: 'ADD_TO_CART', label: 'Add to Cart' },
            { value: 'VIEW_PRODUCTS', label: 'View Products' }
          ]
        },
        {
          label: 'Conversion',
          options: [
            { value: 'SIGN_UP', label: 'Sign Up' },
            { value: 'DOWNLOAD', label: 'Download' },
            { value: 'LEARN_MORE', label: 'Learn More' },
            { value: 'SUBSCRIBE', label: 'Subscribe' },
            { value: 'APPLY_NOW', label: 'Apply Now' }
          ]
        },
        {
          label: 'Engagement',
          options: [
            { value: 'OPEN_LINK', label: 'Open Link' },
            { value: 'CONTACT_US', label: 'Contact Us' },
            { value: 'MESSAGE_US', label: 'Message Us' },
            { value: 'BOOK_NOW', label: 'Book Now' },
            { value: 'GET_QUOTE', label: 'Get Quote' },
            { value: 'GET_OFFER', label: 'Get Offer' }
          ]
        }
      ]
    }
  },
  computed: {
    // Get the token needed for upload
    uploadHeaders() {
      return {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    },
    
    // Determine if it is edit mode
    isEdit() {
      return this.$route.params.id !== undefined
    },
    
    // Preselected ad set ID (obtained from URL parameters)
    preselectedAdsetId() {
      return this.$route.query.adset_id || this.$route.params.adset_id
    },
    
  },
  watch: {
    // Watch for changes in creative type and reset creative URL
    /*'formData.adcreative_type': function(newVal) {
      if (newVal !== 'CAROUSEL') {
        this.formData.adcreative.imageUrl = ''
        this.formData.adcreative.videoUrl = ''
      } else {
        this.formData.adcreative.carouselImages = []
        this.carouselFileList = []
      }
    },*/
    
    // Watch for changes in preselected ad set ID
    preselectedAdsetId: {
      handler(newVal) {
        if (newVal) {
          this.formData.adset_id = newVal
        }
      },
      immediate: true
    }
  },
  methods: {
    // Fetch ad set options
    async fetchAdsetOptions() {
      try {
        const res = await getAdsetOptions()
        this.adsetOptions = res.data || []
      } catch (error) {
        console.error('Failed to get ad set list:', error)
        ElMessage.error('Failed to get ad set list')
      }
    },
    
    // Fetch ad details
    async fetchAdDetail(id) {
      this.loading = true
      try {
        const res = await getAdDetail(id)
        if(res.code == 0){  
          Object.assign(this.formData, res.data)
          if (this.formData.adcreative_type === 'IMAGE') {
              this.formData.adcreative.imageUrl = this.formData.adcreatives[0].image_url
              this.formData.adcreative.imageHash = this.formData.adcreatives[0].object_story_spec.link_data.image_hash
              this.formData.adcreative.name = this.formData.adcreatives[0].object_story_spec.link_data.name
              this.formData.adcreative.title = this.formData.adcreatives[0].object_story_spec.link_data.name
              this.formData.adcreative.message = this.formData.adcreatives[0].object_story_spec.link_data.message
              this.formData.adcreative.callToAction = this.formData.adcreatives[0].object_story_spec.link_data.call_to_action.type
              this.formData.adcreative.linkUrl = this.formData.adcreatives[0].object_story_spec.link_data.link

              this.imageFileList = [{
                name: this.formData.adcreatives[0].object_story_spec.link_data.name,
                url: this.formData.adcreatives[0].image_url
              }]
            }
            else if (this.formData.adcreative_type === 'VIDEO') {
              this.formData.adcreative.videoId = this.formData.adcreatives[0].object_story_spec.video_data.video_id
              this.formData.adcreative.videoUrl = this.formData.adcreatives[0].object_story_spec.video_data.video_url
              this.formData.adcreative.name = this.formData.adcreatives[0].object_story_spec.video_data.title
              this.formData.adcreative.title = this.formData.adcreatives[0].object_story_spec.video_data.title
              this.formData.adcreative.message = this.formData.adcreatives[0].object_story_spec.video_data.message
              this.formData.adcreative.callToAction = this.formData.adcreatives[0].object_story_spec.video_data.call_to_action.type
              this.formData.adcreative.linkUrl = this.formData.adcreatives[0].object_story_spec.video_data.call_to_action.value.link

              this.videoFileList = [{
                name: this.formData.adcreatives[0].object_story_spec.video_data.title,
                url: this.formData.adcreatives[0].object_story_spec.video_data.video_url
              }]
              if (this.formData.adcreatives[0].object_story_spec.video_data.image_url) {
                this.imageFileList = [{
                  name: 'Cover Image',
                  url: this.formData.adcreatives[0].object_story_spec.video_data.image_url
                }]
              }
            }
        }else{
          ElMessage.error(res.msg)
        }
        
        // Handle creative content preview
        if (this.formData.adcreative_type === 'IMAGE' && this.formData.adcreative.imageUrl) {
          this.imageFileList = [{
            name: 'Uploaded Image',
            url: this.formData.adcreative.imageUrl
          }]
        } else if (this.formData.adcreative_type === 'VIDEO') {
          if (this.formData.adcreative.videoUrl) {
            this.videoFileList = [{
              name: 'Uploaded Video',
              url: this.formData.adcreative.videoUrl
            }]
          }
          if (this.formData.adcreative.imageUrl) {
            this.imageFileList = [{
              name: 'Uploaded Image',
              url: this.formData.adcreative.imageUrl
            }]
          }
        } else if (this.formData.adcreative_type === 'CAROUSEL' && this.formData.adcreative.carouselImages) {
          this.carouselFileList = this.formData.adcreative.carouselImages.map((img, index) => ({
            name: `Carousel Image ${index + 1}`,
            url: img.url
          }))
        }
      } catch (error) {
        console.error('Failed to get ad details:', error)
        ElMessage.error('Failed to get ad details')
      } finally {
        this.loading = false
      }
    },
    
    // Image upload handling
    handleImageChange(file, fileList) {
      // Check file type and size
      const isImage = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
      const isLt5M = file.raw.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('Uploaded image can only be JPG or PNG format!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      if (!isLt5M) {
        ElMessage.error('Uploaded image size cannot exceed 5MB!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }

      this.imageFileList = fileList
      this.uploadImage()
    },
    
    handleImageRemove() {
      this.formData.adcreative.imageUrl = ''
      this.imageFileList = []
    },
    
    // Video upload handling
    handleVideoChange(file, fileList) {
      // Check file type and size
      const isMP4 = file.raw.type === 'video/mp4'
      const isMOV = file.raw.type === 'video/quicktime'
      const isLt10M = file.raw.size / 1024 / 1024 < 10

      if (!isMP4 && !isMOV) {
        ElMessage.error('Video can only be MP4 or MOV format!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      if (!isLt10M) {
        ElMessage.error('Video size cannot exceed 10MB!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }

      this.videoFileList = fileList
      this.uploadVideo()
    },
    
    handleVideoRemove() {
      this.formData.adcreative.videoUrl = ''
      this.videoFileList = []
    },
    
    // Carousel image upload handling
    handleCarouselChange(file, fileList) {
      // Check file type and size
      const isImage = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
      const isLt5M = file.raw.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('Uploaded image can only be JPG or PNG format!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      if (!isLt5M) {
        ElMessage.error('Uploaded image size cannot exceed 5MB!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }

      this.carouselFileList = fileList
      this.uploadCarousel()
    },
    
    handleCarouselRemove(file, fileList) {
      this.carouselFileList = fileList
      this.formData.adcreative.carouselImages = fileList.map(item => ({
        url: item.url || item.response.url,
        name: item.name
      }))
    },
    
    // Open media library selection
    openMediaLibrary(type) {
      this.mediaType = type
      this.mediaLibraryVisible = true
    },
    
    // Handle media selection
    handleMediaSelected(data) {
      const { media, type } = data
      
      if (type === 'IMAGE') {
        this.formData.adcreative.imageHash = media.hash
        this.formData.adcreative.imageUrl = media.url
        this.imageFileList = [{
          name: media.name,
          url: media.url
        }]
      } else if (type === 'VIDEO') {
        console.log(media)
        this.formData.adcreative.videoId = media.id
        this.formData.adcreative.videoUrl = media.source
        this.videoFileList = [{
          name: media.title,
          url: media.source
        }]
      }
      
      this.mediaLibraryVisible = false
      ElMessage.success('Material selected from media library')
    },
    
    // Upload image
    async uploadImage() {
      this.loading = true  
      try {
        const formData = new FormData()
        this.imageFileList.forEach(file => {
          formData.append('files[]', file.raw)
        })
        const result = await uploadImage(formData)
        if(result.code == 0){
          this.formData.adcreative.imageHash = result.data.hash
          this.formData.adcreative.imageUrl = result.data.url
        }else{
          ElMessage.error(result.msg)
        } 
      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.loading = false
      }
    },

    // Upload video
    async uploadVideo() {
      try {
        this.loading = true
        const formData = new FormData()
        const fileName = this.videoFileList[0].name;
        formData.append('file', this.videoFileList[0].raw)
        formData.append('title', fileName)

        const result = await uploadVideo(formData)

        if(result.code == 0){
          this.formData.adcreative.videoId = result.data.id
          this.formData.adcreative.videoUrl = result.data.source
          ElMessage.success('Video uploaded successfully')
        }else{
          ElMessage.error(result.msg)
        }
      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.loading = false
      }
    },
    
    // Upload carousel images
    async uploadCarousel() {
      this.loading = true  
      try {
        const formData = new FormData()
        this.carouselFileList.forEach(file => {
          formData.append('files[]', file.raw)
        })
        const result = await uploadImage(formData)
        if(result.code == 0){
          this.formData.carouselImages = result.data.map(item => ({
            url: item.url,
            name: item.name
          }))
        }else{
          ElMessage.error(result.msg)
        } 
      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.loading = false
      }
    },
    
    // Get CTA label
    getCtaLabel(value) {
      for (const group of this.ctaOptions) {
        const option = group.options.find(item => item.value === value)
        if (option) return option.label
      }
      return value
    },
    
    // Submit form
    async submitForm() {
      if (!this.$refs.formRef) return
      
      await this.$refs.formRef.validate(async (valid, fields) => {
        if (valid) {
          this.submitting = true
          
          try {
            // Build submit data
            const submitData = { ...this.formData }
            
            // Handle redirect URL
            if (submitData.trackingParams) {
              const separator = submitData.adcreative.linkUrl.includes('?') ? '&' : '?'
              submitData.adcreative.linkUrl += separator + submitData.trackingParams
            }
            
            // Create or update ad based on mode
            if (this.isEdit) {
              await updateAd(this.formData.id, submitData)
              ElMessage.success('Ad updated successfully')
            } else {
              await createAd(submitData)
              ElMessage.success('Ad created successfully')
            }
            
            // Redirect back to list page
            this.goBack()
          } catch (error) {
            console.error('Form submission failed:', error)
            ElMessage.error('Submission failed: ' + (error.message || 'Unknown error'))
          } finally {
            this.submitting = false
          }
        } else {
          console.log('Form validation failed:', fields)
          ElMessage.error('Please check if the form is filled out correctly')
        }
      })
    },
    
    // Go back to the previous page
    goBack() {
      this.$router.back()
    },
    
    // Reset form
    resetForm() {
      this.formRef.resetFields()
    },

    // Open creative selector
    openCreativeSelector() {
      this.creativeSelectorVisible = true
    },
    
    // Handle creative selection
    handleCreativeSelected(creative) {
      // Update form data
      this.formData.creative_id = creative.id
      this.formData.adcreative_type = creative.type
      
      if (creative.type === 'IMAGE') {
          this.formData.adcreative = {
          ...this.formData.adcreative,
          imageHash: creative.object_story_spec.link_data.image_hash,
          imageUrl: creative.image_url,
          name: creative.object_story_spec.link_data.name,
          title: creative.object_story_spec.link_data.name,
          message: creative.object_story_spec.link_data.message,
          callToAction: creative.object_story_spec.link_data.call_to_action.type,
          linkUrl: creative.object_story_spec.link_data.link
        }

        this.imageFileList = [{
          name: creative.object_story_spec.link_data.name,
          url: creative.image_url
        }]
      }
      else if (creative.type === 'VIDEO') {
        this.formData.adcreative = {
          ...this.formData.adcreative,
          videoId: creative.object_story_spec.video_data.video_id,
          videoUrl: creative.object_story_spec.video_data.video_url,
          name: creative.object_story_spec.video_data.title,
          title: creative.object_story_spec.video_data.title,
          message: creative.object_story_spec.video_data.message,
          callToAction: creative.object_story_spec.video_data.call_to_action.type,
          linkUrl: creative.object_story_spec.video_data.call_to_action.value.link
        }

        this.videoFileList = [{
          name: creative.object_story_spec.video_data.title,
          url: creative.object_story_spec.video_data.video_url
        }]
        if (creative.thumbnail_url) {
          this.imageFileList = [{
            name: 'Cover Image',
            url: creative.thumbnail_url
          }]
        }
      }

      this.creativeSelectorVisible = false
      ElMessage.success('Creative selected')
    },
  },
  mounted() {
    this.fetchAdsetOptions()
    
    // Fetch ad details in edit mode
    if (this.isEdit) {
      this.fetchAdDetail(this.$route.params.id)
    }
  }
}
</script>

<style lang="scss" scoped>
.ad-form-container {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px dashed #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 20px 0;
    padding-left: 10px;
    border-left: 3px solid #409eff;
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 5px;
    margin-left: 10px;
  }
  
  .creative-content-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
  }
  
  .creative-content-form {
    width: 60%;
  }
  
  .creative-preview {
    width: 40%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    background-color: #f5f7fa;
  }
  
  .preview-header {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;
  }
  
  .preview-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .preview-media {
    width: 100%;
    max-height: 300px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 10px;
  }
  
  .preview-image {
    max-width: 100%;
    max-height: 250px;
  }
  
  .preview-video {
    max-width: 100%;
    max-height: 250px;
  }
  
  .preview-content {
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fff;
  }
  
  .preview-message {
    margin: 10px 0;
    color: #606266;
  }
  
  .preview-cta {
    margin: 10px 0;
  }
  
  .preview-link {
    margin: 10px 0;
    word-break: break-all;
  }
  
  .preview-link a {
    color: #409eff;
    text-decoration: none;
  }
  
  .preview-link a:hover {
    text-decoration: underline;
  }
  
  .creative-uploader {
    margin-bottom: 10px;
  }
  
  .upload-icon {
    font-size: 28px;
    color: #8c939d;
  }
  
  .uploaded-image,
  .uploaded-video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style> 