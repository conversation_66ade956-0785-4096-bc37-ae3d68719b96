import { getFBAdAccountList } from '@/api/options'
import { getFBUserOptionsList } from '@/api/fbuser'

const fbOptions = {
  state: {
    // FB选项状态 - 从localStorage恢复
    fbUserId: localStorage.getItem('fbUserId') || null,
    fbAdAccountId: localStorage.getItem('fbAdAccountId') || null,
    fbUserList: [],
    fbAdAccountList: []
  },

  mutations: {
    // FB选项相关mutations
    SET_FB_USER_ID(state, fbUserId) {
      state.fbUserId = fbUserId
      // 保存到localStorage
      if (fbUserId) {
        localStorage.setItem('fbUserId', fbUserId)
      } else {
        localStorage.removeItem('fbUserId')
      }
      // 清空广告账户选择
      state.fbAdAccountId = null
      localStorage.removeItem('fbAdAccountId')
      state.fbAdAccountList = []
    },
    
    SET_FB_AD_ACCOUNT_ID(state, fbAdAccountId) {
      state.fbAdAccountId = fbAdAccountId
      // 保存到localStorage
      if (fbAdAccountId) {
        localStorage.setItem('fbAdAccountId', fbAdAccountId)
      } else {
        localStorage.removeItem('fbAdAccountId')
      }
    },
    
    SET_FB_USER_LIST(state, fbUserList) {
      state.fbUserList = fbUserList
    },
    
    SET_FB_AD_ACCOUNT_LIST(state, fbAdAccountList) {
      state.fbAdAccountList = fbAdAccountList
    },

    // 清空FB选项状态
    CLEAR_FB_OPTIONS(state) {
      state.fbUserId = null
      state.fbAdAccountId = null
      state.fbUserList = []
      state.fbAdAccountList = []
      // 清理localStorage
      localStorage.removeItem('fbUserId')
      localStorage.removeItem('fbAdAccountId')
    }
  },

  actions: {
    // FB选项相关actions
    async loadFBUserList({ commit }) {
      try {
        const response = await getFBUserOptionsList()
        if (response.code === 0) {
          console.log('loadFBUserList.data', response.data)
          commit('SET_FB_USER_LIST', response.data || [])
          return response.data || []
        } else {
          throw new Error(response.msg || '获取FB用户列表失败')
        }
      } catch (error) {
        console.error('加载FB用户列表失败:', error)
        commit('SET_FB_USER_LIST', [])
        throw error
      }
    },

    async loadFBAdAccountList({ commit, state }) {
      if (!state.fbUserId) {
        commit('SET_FB_AD_ACCOUNT_LIST', [])
        return []
      }
      
      try {
        const response = await getFBAdAccountList(state.fbUserId)
        if (response.code === 0) {
          // 过滤当前选中用户的广告账户
          const accounts = response.data
          commit('SET_FB_AD_ACCOUNT_LIST', accounts)
          return accounts
        } else {
          throw new Error(response.msg || '获取FB广告账户列表失败')
        }
      } catch (error) {
        console.error('加载FB广告账户列表失败:', error)
        commit('SET_FB_AD_ACCOUNT_LIST', [])
        throw error
      }
    },

    setFBUser({ commit, dispatch }, fbUserId) {
      commit('SET_FB_USER_ID', fbUserId)
      // 重新加载广告账户列表
      if (fbUserId) {
        dispatch('loadFBAdAccountList')
      }
    },

    setFBAdAccount({ commit }, fbAdAccountId) {
      commit('SET_FB_AD_ACCOUNT_ID', fbAdAccountId)
    },

    // 清空所有FB选项
    clearFBOptions({ commit }) {
      commit('CLEAR_FB_OPTIONS')
    }
  }
}

export default fbOptions 