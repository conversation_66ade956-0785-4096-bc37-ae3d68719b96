<template>
  <div class="adset-form-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? 'Edit Ad Set' : 'Create Ad Set' }}</span>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            <span>Back</span>
          </el-button>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="200px"
        label-position="right"
        :disabled="loading"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">Basic Information</h3>
          
          <el-form-item label="Ad Set Name" prop="name" required>
            <el-input 
              v-model="formData.name"
              placeholder="Please enter ad set name"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="Ad Set ID" v-if="isEdit">
            <el-input v-model="formData.id" disabled />
          </el-form-item>
          
          <el-form-item label="Campaign" prop="campaign_id" required>
            <el-select
              v-model="formData.campaign_id"
              placeholder="Please select campaign"
              style="width: 100%"
              filterable
              :disabled="isEdit || !!preselectedCampaignId"
            >
              <el-option
                v-for="item in campaignOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="Daily Budget" prop="daily_budget" required>
            <el-input-number
              v-model="formData.daily_budget"
              :min="0"
              :precision="2"
              :step="10"
              style="width: 30%"
            />
            <div class="form-tip">Set daily budget limit, unit: CNY</div>
          </el-form-item>
          
          <el-form-item label="Schedule" prop="start_time">
            <div class="time-range-container">
              <el-date-picker
                v-model="formData.start_time"
                type="datetime"
                placeholder="Select start time"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 45%"
                :disabled="isEdit"
              />
              <span class="time-separator">to</span>
              <el-date-picker
                v-model="formData.end_time"
                type="datetime"
                placeholder="Select end time"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 45%"
              />
            </div>
            <div class="form-tip">Ad set schedule must be within the campaign schedule</div>
          </el-form-item>
          
          <el-form-item label="Status" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio-button  label="ACTIVE">Active</el-radio-button>
              <el-radio-button label="PAUSED">Paused</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
        
        <!-- Optimization Settings -->
        <div class="form-section">
          <h3 class="section-title">Optimization Settings</h3>
          
          <el-form-item label="Optimization Goal" prop="optimization_goal" required>
            <el-select
              v-model="formData.optimization_goal"
              placeholder="Please select optimization goal"
              style="width: 100%"
            >
              <el-option
                v-for="item in optimizationGoalOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="Destination Type" prop="destination_type" required>
            <el-select
              v-model="formData.destination_type"
              placeholder="Please select destination type"
              style="width: 100%"
            >
              <el-option
                v-for="item in destinationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">Select the final destination for your ad</div>
          </el-form-item>
          
          <el-form-item label="Billing Event" prop="billing_event" required>
            <el-select
              v-model="formData.billing_event"
              placeholder="Please select billing event"
              style="width: 100%"
            >
              <el-option
                v-for="item in billingEventOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="Bid Strategy" prop="bid_strategy" required>
            <el-select
              v-model="formData.bid_strategy"
              placeholder="Please select bid strategy"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="item in bidStrategyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item 
            label="Bid Amount" 
            prop="bid_amount"
             v-if="formData.bid_strategy === 'LOWEST_COST_WITH_BID_CAP' || formData.bid_strategy === 'COST_CAP'"
          >
            <el-input-number
              v-model="formData.bid_amount"
              :min="0"
              :precision="2"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">
              {{ formData.bid_strategy === 'LOWEST_COST_WITH_BID_CAP' ? 'Bid Cap' : 'Cost Cap' }}, unit: CNY
            </div>
          </el-form-item>

          <!-- 添加 promoted_object 字段 -->
          <el-form-item label="Pixel ID" prop="promoted_object.pixel_id">
            <el-input
              v-model="formData.promoted_object.pixel_id"
              placeholder="Please enter Pixel ID"
              style="width: 100%"
            />
            <div class="form-tip">Pixel ID for tracking conversion events</div>
          </el-form-item>

          <el-form-item label="Conversion Event Type" prop="promoted_object.custom_event_type" v-if="formData.promoted_object.pixel_id">
            <el-select
              v-model="formData.promoted_object.custom_event_type"
              placeholder="Please select conversion event type"
              style="width: 100%"
            >
              <el-option
                v-for="item in customEventTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">Select the type of conversion event to track</div>
          </el-form-item>
        </div>
        
        <!-- Targeting Settings -->
        <div class="form-section">
          <h3 class="section-title">Targeting Settings</h3>
          
          <el-form-item label="Geographic Location" prop="targetingObj.geo_locations.countries" required>
            <el-select
              v-model="formData.targetingObj.geo_locations.countries"
              placeholder="Please select countries/regions"
              style="width: 100%"
              multiple
              filterable
            >
              <el-option
                v-for="item in countryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="Age Range" required>
            <div class="age-range-container">
              <el-form-item prop="targetingObj.age_min" class="age-input">
                <el-input-number
                  v-model="formData.targetingObj.age_min"
                  :min="18"
                  :max="formData.targetingObj.age_max || 65"
                  placeholder="Min age"
                  @change="handleAgeChange"
                />
              </el-form-item>
              <span class="age-separator">-</span>
              <el-form-item prop="targetingObj.age_max" class="age-input">
                <el-input-number
                  v-model="formData.targetingObj.age_max"
                  :min="formData.targetingObj.age_min || 18"
                  :max="65"
                  placeholder="Max age"
                  @change="handleAgeChange"
                />
              </el-form-item>
            </div>
            <div class="form-tip">Age range must be between 18-65 years</div>
          </el-form-item>
          
          <el-form-item label="Gender" prop="targetingObj.genders">
            <el-checkbox-group v-model="formData.targetingObj.genders">
              <el-checkbox label="1">Male</el-checkbox>
              <el-checkbox label="2">Female</el-checkbox>
            </el-checkbox-group>
            <div class="form-tip">No selection means all genders</div>
          </el-form-item>
          
          <!--<el-form-item label="兴趣" prop="targeting.interests">
            <el-select
              v-model="formData.targeting.interests"
              placeholder="请选择兴趣类别"
              style="width: 100%"
              multiple
              filterable
              remote
              :remote-method="searchInterests"
              :loading="interestsLoading"
            >
              <el-option
                v-for="item in interestOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <div class="form-tip">选择用户感兴趣的领域进行精准定位</div>
          </el-form-item>
          
          <el-form-item label="行为" prop="targeting.behaviors">
            <el-select
              v-model="formData.targeting.behaviors"
              placeholder="请选择用户行为"
              style="width: 100%"
              multiple
              filterable
              remote
              :remote-method="searchBehaviors"
              :loading="behaviorsLoading"
            >
              <el-option
                v-for="item in behaviorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>-->
        </div>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">{{ isEdit ? 'Save Changes' : 'Create Ad Set' }}</el-button>
          <el-button @click="resetForm" :disabled="loading">Reset</el-button>
          <el-button @click="goBack" :disabled="loading">Cancel</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdsetDetail, createAdset, updateAdset } from '@/api/adset'
import { getCampaignOptions } from '@/api/campaign'
import { getCountryList } from '@/api/options'

export default {
  name: 'AdSetForm',
  
  data() {
    return {
      loading: false,
      formData: {
        id: '',
        name: '',
        campaign_id: '',
        daily_budget: 0,
        start_time: '',
        end_time: '',
        status: 'PAUSED',
        optimization_goal: 'REACH',
        destination_type: 'WEBSITE',
        billing_event: 'IMPRESSIONS',
        bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
        bid_amount: 0,
        targeting: {
          countries: ['CN'],
          ageRange: '18-65+',
          genders: [],
          interests: [],
          behaviors: []
        },
        targetingObj: {
          geo_locations: {
            countries: []
          },
          age_min: 18,
          age_max: 65,
          genders: []
        },
        promoted_object: {
          pixel_id: null,
          custom_event_type: ''
        }
      },
      campaignOptions: [],
      countryOptions: [],
      interestOptions: [],
      behaviorOptions: [],
      interestsLoading: false,
      behaviorsLoading: false,
      optimizationGoalOptions: [
        { value: 'REACH', label: 'Reach' },
        { value: 'IMPRESSIONS', label: 'Impressions' },
        { value: 'LINK_CLICKS', label: 'Link Clicks' },
        { value: 'PAGE_LIKES', label: 'Page Likes' },
        { value: 'APP_INSTALLS', label: 'App Installs' },
        { value: 'VIDEO_VIEWS', label: 'Video Views' },
        { value: 'LEAD_GENERATION', label: 'Lead Generation' },
        { value: 'CONVERSIONS', label: 'Conversions' },
        { value: 'LANDING_PAGE_VIEWS', label: 'Landing Page Views' }
      ],
              billingEventOptions: [
          { value: 'IMPRESSIONS', label: 'Impressions' },
          { value: 'LINK_CLICKS', label: 'Link Clicks' },
          { value: 'APP_INSTALLS', label: 'App Installs' },
          { value: 'VIDEO_VIEWS', label: 'Video Views' },
          { value: 'CONVERSIONS', label: 'Conversions' }
        ],
              bidStrategyOptions: [
          { value: 'LOWEST_COST_WITHOUT_CAP', label: 'Automatic Bidding' },
          { value: 'LOWEST_COST_WITH_MIN_ROAS', label: 'Lowest Cost (with ROAS)' },
          { value: 'LOWEST_COST_WITH_BID_CAP', label: 'Lowest Cost (with Bid Cap)' },
          { value: 'COST_CAP', label: 'Cost Cap' }
        ],
              destinationTypeOptions: [
          { value: 'WEBSITE', label: 'Website' },
          { value: 'APP', label: 'App' },
          { value: 'MESSENGER', label: 'Messenger' },
          { value: 'INSTAGRAM', label: 'Instagram' },
          { value: 'FACEBOOK', label: 'Facebook' },
          { value: 'WHATSAPP', label: 'WhatsApp' }
        ],
      customEventTypeOptions: [
        { value: 'AD_IMPRESSION', label: 'AD_IMPRESSION' },
        { value: 'RATE', label: 'RATE' },
        { value: 'TUTORIAL_COMPLETION', label: 'TUTORIAL_COMPLETION' },
        { value: 'CONTACT', label: 'CONTACT' },
        { value: 'CUSTOMIZE_PRODUCT', label: 'CUSTOMIZE_PRODUCT' },
        { value: 'DONATE', label: 'DONATE' },
        { value: 'FIND_LOCATION', label: 'FIND_LOCATION' },
        { value: 'SCHEDULE', label: 'SCHEDULE' },
        { value: 'START_TRIAL', label: 'START_TRIAL' },
        { value: 'SUBMIT_APPLICATION', label: 'SUBMIT_APPLICATION' },
        { value: 'SUBSCRIBE', label: 'SUBSCRIBE' },
        { value: 'ADD_TO_CART', label: 'ADD_TO_CART' },
        { value: 'ADD_TO_WISHLIST', label: 'ADD_TO_WISHLIST' },
        { value: 'INITIATED_CHECKOUT', label: 'INITIATED_CHECKOUT' },
        { value: 'ADD_PAYMENT_INFO', label: 'ADD_PAYMENT_INFO' },
        { value: 'PURCHASE', label: 'PURCHASE' },
        { value: 'LEAD', label: 'LEAD' },
        { value: 'COMPLETE_REGISTRATION', label: 'COMPLETE_REGISTRATION' },
        { value: 'CONTENT_VIEW', label: 'CONTENT_VIEW' },
        { value: 'SEARCH', label: 'SEARCH' },
        { value: 'SERVICE_BOOKING_REQUEST', label: 'SERVICE_BOOKING_REQUEST' },
        { value: 'MESSAGING_CONVERSATION_STARTED_7D', label: 'MESSAGING_CONVERSATION_STARTED_7D' },
        { value: 'LEVEL_ACHIEVED', label: 'LEVEL_ACHIEVED' },
        { value: 'ACHIEVEMENT_UNLOCKED', label: 'ACHIEVEMENT_UNLOCKED' },
        { value: 'SPENT_CREDITS', label: 'SPENT_CREDITS' },
        { value: 'LISTING_INTERACTION', label: 'LISTING_INTERACTION' },
        { value: 'D2_RETENTION', label: 'D2_RETENTION' },
        { value: 'D7_RETENTION', label: 'D7_RETENTION' },
        { value: 'OTHER', label: 'OTHER' }
      ],
      rules: {
        name: [
          { required: true, message: 'Please enter ad set name', trigger: 'blur' },
          { min: 2, max: 250, message: 'Length should be between 2 and 250 characters', trigger: 'blur' }
        ],
        campaign_id: [
          { required: true, message: 'Please select campaign', trigger: 'change' }
        ],
        daily_budget: [
          { required: true, message: 'Please set daily budget', trigger: 'blur' },
          //{ type: 'number', min: 1, message: 'Daily budget must be greater than 0', trigger: 'blur' }
        ],
        optimization_goal: [
          { required: true, message: 'Please select optimization goal', trigger: 'change' }
        ],
        destination_type: [
          { required: true, message: 'Please select destination type', trigger: 'change' }
        ],
        billing_event: [
          { required: true, message: 'Please select billing event', trigger: 'change' }
        ],
        bid_strategy: [
          { required: true, message: 'Please select bid strategy', trigger: 'change' }
        ],
        'targetingObj.countries': [
          { required: true, message: 'Please select at least one country/region', trigger: 'change' }
        ],
        'targetingObj.age_min': [
          { required: true, message: 'Please set minimum age', trigger: 'blur' },
          { type: 'number', min: 18, max: 65, message: 'Minimum age must be between 18-65', trigger: 'blur' }
        ],
        'targetingObj.age_max': [
          { required: true, message: 'Please set maximum age', trigger: 'blur' },
          { type: 'number', min: 18, max: 65, message: 'Maximum age must be between 18-65', trigger: 'blur' }
        ],
        start_time: [
          { required: false, message: 'Please select start time', trigger: 'change' }
        ],
        end_time: [
          { required: false, message: 'Please select end time', trigger: 'change' }
        ],
        /*'promoted_object.pixel_id': [
          { required: true, message: 'Please enter Pixel ID', trigger: 'blur' }
        ],
        'promoted_object.custom_event_type': [
          { required: true, message: 'Please select conversion event type', trigger: 'change' }
        ]*/
      }
    }
  },
  
  computed: {
    isEdit() {
      return this.$route.params.id !== undefined
    },
    
    preselectedCampaignId() {
      return this.$route.params.campaign_id
    }
  },
  
  mounted() {
    this.initData()
  },
  
  methods: {
    async initData() {
      await Promise.all([
        this.fetchCampaignOptions(),
        this.fetchCountryOptions()
      ])
      
      if (this.isEdit && this.$route.params.id) {
        this.fetchAdsetDetail(this.$route.params.id)
      }
    },
    
    async fetchCampaignOptions() {
      try {
        // 使用预选的广告系列ID
        if (this.preselectedCampaignId) {
          this.formData.campaign_id = this.preselectedCampaignId
        }
        
        // 实际API调用:
        const res = await getCampaignOptions()
        this.campaignOptions = res.data || []
      } catch (error) {
        console.error('Failed to get campaign list:', error)
        ElMessage.error('Failed to get campaign list')
      }
    },
    
    async fetchCountryOptions() {
      try {
        // 实际API调用:
        const res = await getCountryList()
        if(res.code === 0){
          this.countryOptions = res.data || []
        }
        else{
          ElMessage.error(res.msg)
        }
      } catch (error) {
        console.error('Failed to get country options:', error)
        ElMessage.error('Failed to get country options')
      }
    },
    
    async searchInterests(query) {
      if (query.length < 2) return
      
      this.interestsLoading = true
      try {
        // 模拟数据
        const mockData = [
          { id: '6003', name: '科技' },
          { id: '6004', name: '手机' },
          { id: '6005', name: '电脑' },
          { id: '6006', name: '游戏' },
          { id: '6007', name: '时尚' },
          { id: '6008', name: '美妆' },
          { id: '6009', name: '旅游' }
        ]
        
        setTimeout(() => {
          this.interestOptions = mockData.filter(item => item.name.includes(query))
          this.interestsLoading = false
        }, 500)
        
        // 实际API调用:
        // const res = await getTargetingSuggestions(query, 'interests')
        // this.interestOptions = res.options || []
      } catch (error) {
        console.error('搜索兴趣失败:', error)
        ElMessage.error('搜索兴趣失败')
        this.interestsLoading = false
      }
    },
    
    async searchBehaviors(query) {
      if (query.length < 2) return
      
      this.behaviorsLoading = true
      try {
        // 模拟数据
        const mockData = [
          { id: '7001', name: '经常网购' },
          { id: '7002', name: '旅行爱好者' },
          { id: '7003', name: '应用内购买' },
          { id: '7004', name: '科技早期采用者' },
          { id: '7005', name: '小企业主' }
        ]
        
        setTimeout(() => {
          this.behaviorOptions = mockData.filter(item => item.name.includes(query))
          this.behaviorsLoading = false
        }, 500)
        
        // 实际API调用:
        // const res = await getTargetingSuggestions(query, 'behaviors')
        // this.behaviorOptions = res.options || []
      } catch (error) {
        console.error('搜索行为失败:', error)
        ElMessage.error('搜索行为失败')
        this.behaviorsLoading = false
      }
    },
    
    async fetchAdsetDetail(id) {
      this.loading = true
      try {
        const res = await getAdsetDetail(id)
        if(res.code === 0){
          Object.assign(this.formData, res.data)
          
          // 确保性别数据格式正确
          if (this.formData.targetingObj && this.formData.targetingObj.genders) {
            // 转换性别数据为字符串格式
            this.formData.targetingObj.genders = this.formData.targetingObj.genders.map(gender => 
              gender.toString()
            )
          }
        }
        else{
          ElMessage.error(res.msg)
        }
      } catch (error) {
        console.error('Failed to get ad set details', error)
        ElMessage.error('Failed to get ad set details')
        this.goBack()
      } finally {
        this.loading = false
      }
    },
    
    async submitForm() {
      if (!this.$refs.formRef) return
      
      try {
        await this.$refs.formRef.validate()
        
        // 检查日期范围
        /*if (!this.formData.start_time || !this.formData.end_time) {
          return ElMessage.warning('请选择投放起止时间')
        }*/

        this.formData.targeting = this.formData.targetingObj
      
        this.loading = true
        
        if (this.isEdit) {
          // 编辑模式
          const res = await updateAdset(this.formData.id, this.formData)
          if(res.code === 0){
            ElMessage.success('Ad set updated successfully')
          }
          else{
            ElMessage.error(res.msg)
          }
        } else {
          // 创建模式
          const res = await createAdset(this.formData)
          if(res.code === 0){
            ElMessage.success('Ad set created successfully')
          }
          else{
            ElMessage.error(res.msg)
          }
        }
        
        this.goBack()
      } catch (error) {
        console.error('Form submission failed', error)
        ElMessage.error('Form validation failed, please check your input')
      } finally {
        this.loading = false
      }
    },
    
    resetForm() {
      if (this.isEdit) {
        // 编辑模式下重新获取详情
        ElMessageBox.confirm('Are you sure you want to reset the form? This will restore the original data.', 'Tip', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          this.fetchAdsetDetail(this.$route.params.id)
        }).catch(() => {})
      } else {
        // 创建模式下重置为默认值
        this.$refs.formRef?.resetFields()
        const defaultFormData = {
          id: '',
          name: '',
          campaign_id: this.preselectedCampaignId || '',
          daily_budget: 50,
          start_time: '',
          end_time: '',
          status: 'PAUSED',
          optimization_goal: 'REACH',
          destination_type: 'WEBSITE',
          billing_event: 'IMPRESSIONS',
          bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
          bid_amount: 0,
          targeting: {
            countries: ['CN'],
            ageRange: '18-65+',
            genders: [],
            interests: [],
            behaviors: []
          },
          targetingObj: {
            geo_locations: {
              countries: []
            },
            age_min: 18,
            age_max: 65,
            genders: []
          },
          promoted_object: {
            pixel_id: null,
            custom_event_type: ''
          }
        }
        Object.assign(this.formData, defaultFormData)
      }
    },
    
    goBack() {
      if (this.preselectedCampaignId) {
        this.$router.push(`/campaigns/${this.preselectedCampaignId}/adsets`)
      } else {
        this.$router.push('/adsets')
      }
    },
    
    handleAgeChange() {
      // 确保最小年龄不大于最大年龄
      if (this.formData.targetingObj.age_min > this.formData.targetingObj.age_max) {
        this.formData.targetingObj.age_max = this.formData.targetingObj.age_min
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.adset-form-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px dashed #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 20px 0;
    padding-left: 10px;
    border-left: 3px solid #409eff;
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 5px;
    margin-left: 10px;
  }
  
  .age-range-container {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .age-input {
      flex: 1;
      margin-bottom: 0;
    }
    
    .age-separator {
      color: #606266;
      font-size: 16px;
    }
  }
  
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 22px;
    
    .budget-item {
      flex: 1;
      margin-bottom: 0;
    }
  }
  
  .date-items-container {
    display: flex;
    gap: 20px;
    margin-bottom: 5px;
    
    .el-form-item {
      flex: 1;
      margin-bottom: 0;
    }
  }
  
  .date-range-tip {
    margin-bottom: 22px;
  }
  
  .time-range-container {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .time-separator {
      color: #606266;
      font-size: 14px;
    }
  }
}
</style> 