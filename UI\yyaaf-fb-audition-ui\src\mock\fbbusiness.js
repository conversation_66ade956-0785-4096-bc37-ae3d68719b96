import Mock from 'mockjs'

// 生成模拟FB业务数据
function generateFBBusinesses(count = 20) {
  const businesses = []
  
  for (let i = 1; i <= count; i++) {
    const businessId = `business_${i.toString().padStart(3, '0')}`
    
    const businessNames = [
      '东南亚电商科技',
      '欧美时尚品牌',
      '日韩美妆集团',
      '拉美游戏工作室',
      '中东金融服务',
      '北美科技创新',
      '澳洲健康生活',
      '非洲农业发展',
      '印度教育平台',
      '巴西娱乐媒体'
    ]
    
    const businessName = Mock.Random.pick(businessNames) + Mock.Random.string('upper', 2, 4)
    const createTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    const updateTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    
    businesses.push({
      id: businessId,
      name: businessName,
      businessId: `fb_biz_${Mock.Random.string('number', 10, 15)}`,
      description: `${businessName}是一家专注于${Mock.Random.pick(['电商', '品牌营销', '数字化转型', '创新科技', '用户体验'])}的公司`,
      website: `https://www.${businessName.toLowerCase().replace(/\s+/g, '')}.com`,
      industry: Mock.Random.pick(['E-commerce', 'Fashion', 'Technology', 'Gaming', 'Finance', 'Health', 'Education', 'Entertainment']),
      country: Mock.Random.pick(['US', 'UK', 'CA', 'AU', 'SG', 'JP', 'KR', 'DE', 'FR', 'BR']),
      timezone: Mock.Random.pick([
        'Asia/Shanghai',
        'America/New_York', 
        'Europe/London',
        'Asia/Tokyo',
        'Europe/Berlin',
        'America/Los_Angeles',
        'Australia/Sydney'
      ]),
      currency: Mock.Random.pick(['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'CAD', 'AUD']),
      status: Mock.Random.pick(['ACTIVE', 'INACTIVE', 'PENDING']),
      verificationStatus: Mock.Random.pick(['VERIFIED', 'UNVERIFIED', 'PENDING']),
      createOn: createTime,
      lastUpdateTime: Math.random() > 0.3 ? updateTime : createTime,
      // 统计信息
      adAccountsCount: Mock.Random.integer(1, 10),
      pagesCount: Mock.Random.integer(1, 15),
      pixelsCount: Mock.Random.integer(0, 5),
      // 额外信息
      email: Mock.Random.email(),
      phone: Mock.Random.pick(['', '******-0123', '+44-20-7946-0958', '+86-138-0013-8000']),
      address: Mock.Random.county(true),
      employeeCount: Mock.Random.integer(10, 1000)
    })
  }
  
  return businesses
}

// 生成模拟数据
const allBusinesses = generateFBBusinesses(50)

// Mock API 接口
Mock.mock(/\/fbbusiness$/, 'get', (options) => {
  const url = new URL('http://localhost' + options.url)
  const params = Object.fromEntries(url.searchParams.entries())
  
  console.log('FB Business API called with params:', params)
  
  const page = parseInt(params.page) || 1
  const limit = parseInt(params.limit) || 20
  const name = params.name || ''
  const status = params.status || ''
  const industry = params.industry || ''
  
  // 过滤数据
  let filteredBusinesses = allBusinesses
  
  // 应用搜索过滤
  if (name) {
    filteredBusinesses = filteredBusinesses.filter(b => 
      b.name.toLowerCase().includes(name.toLowerCase())
    )
  }
  
  if (status) {
    filteredBusinesses = filteredBusinesses.filter(b => b.status === status)
  }
  
  if (industry) {
    filteredBusinesses = filteredBusinesses.filter(b => b.industry === industry)
  }
  
  // 分页
  const start = (page - 1) * limit
  const end = start + limit
  const list = filteredBusinesses.slice(start, end)
  
  return {
    code: 0,
    message: 'success',
    data: list,
    total: filteredBusinesses.length,
    page: page,
    limit: limit,
    pages: Math.ceil(filteredBusinesses.length / limit)
  }
})

// Mock 业务详情API
Mock.mock(/\/fbbusiness\/(.+)/, 'get', (options) => {
  const businessId = options.url.split('/').pop()
  console.log('FB Business Detail API called for businessId:', businessId)

  const business = allBusinesses.find(b => b.id === businessId)

  if (!business) {
    return {
      code: 1,
      message: '业务不存在',
      data: null
    }
  }

  // 返回详细信息
  return {
    code: 0,
    message: 'success',
    data: {
      ...business,
      // 额外的详情信息
      totalSpend: Mock.Random.float(1000, 100000, 2, 2),
      totalRevenue: Mock.Random.float(5000, 500000, 2, 2),
      activeAds: Mock.Random.integer(10, 100),
      activeCampaigns: Mock.Random.integer(5, 50),
      lastLoginTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      settings: {
        autoOptimization: Mock.Random.boolean(),
        budgetAlerts: Mock.Random.boolean(),
        performanceReports: Mock.Random.boolean()
      }
    }
  }
})

export default {
  allBusinesses,
  generateFBBusinesses
}
