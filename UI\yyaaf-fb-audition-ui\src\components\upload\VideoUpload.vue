<template>
  <div class="video-upload-container">
    <el-upload
      v-model:file-list="fileList"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-exceed="handleExceed"
      :limit="limit"
      :headers="headers"
      :multiple="multiple"
      :disabled="disabled"
      :class="{ 'hide-upload': fileList.length >= limit && !multiple }"
    >
      <el-button type="primary" :disabled="disabled">
        <el-icon class="upload-icon"><Upload /></el-icon>
        <span>Upload Video</span>
      </el-button>
      <template #tip>
        <div class="upload-tip">
          {{ tipText }}
        </div>
      </template>
    </el-upload>
    
    <!-- Upload list -->
    <div class="video-list" v-if="fileList.length > 0">
      <div v-for="(file, index) in fileList" :key="index" class="video-item">
        <div class="video-item-info">
          <div class="video-thumbnail">
            <el-image 
              :src="file.thumbnail || defaultThumbnail" 
              fit="cover"
              @click="handlePreview(file)"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><VideoCameraFilled /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="video-duration" v-if="file.duration">{{ formatDuration(file.duration) }}</div>
          </div>
          
          <div class="video-details">
            <div class="video-name" :title="file.name">{{ file.name }}</div>
            <div class="video-status">
              <el-progress 
                v-if="file.status === 'uploading'" 
                :percentage="file.percentage" 
                :stroke-width="4"
                status="primary"
              />
              <el-tag v-else-if="file.status === 'success'" type="success" size="small">Upload successful</el-tag>
              <el-tag v-else-if="file.status === 'error'" type="danger" size="small">Upload failed</el-tag>
            </div>
          </div>
        </div>
        
        <div class="video-actions">
          <el-button type="primary" link @click="handlePreview(file)">
            <el-icon><View /></el-icon>
            Preview
          </el-button>
          <el-button type="danger" link @click="handleRemove(file)">
            <el-icon><Delete /></el-icon>
            Delete
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- Video preview dialog -->
    <el-dialog 
      v-model="previewVisible" 
      append-to-body
      :title="previewFile?.name || 'Video Preview'"
      width="65%"
      destroy-on-close
    >
      <div class="video-preview">
        <video 
          v-if="previewUrl" 
          controls 
          :src="previewUrl" 
          class="preview-video"
        ></video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // Upload URL
  action: {
    type: String,
    default: '/api/upload/video'
  },
  // List of uploaded files
  modelValue: {
    type: Array,
    default: () => []
  },
  // Limit on the number of uploads
  limit: {
    type: Number,
    default: 3
  },
  // Whether multiple selection is supported
  multiple: {
    type: Boolean,
    default: false
  },
  // Whether to disable
  disabled: {
    type: Boolean,
    default: false
  },
  // Tip text
  tipText: {
    type: String,
    default: 'Supports mp4/mov/avi format, single video does not exceed 100MB'
  },
  // Request headers
  customHeaders: {
    type: Object,
    default: () => ({})
  },
  // Default thumbnail
  defaultThumbnail: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'success', 'progress', 'error', 'remove', 'exceed'])

// Upload URL
const uploadUrl = computed(() => props.action)

// Request header information
const headers = computed(() => {
  return {
    ...props.customHeaders,
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
})

// File list
const fileList = ref([])

// Preview related status
const previewVisible = ref(false)
const previewUrl = ref('')
const previewFile = ref(null)

// 同步外部传入的文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length >= 0) {
      fileList.value = [...newVal]
    }
  },
  { immediate: true, deep: true }
)

// Validate before upload
const beforeUpload = (file) => {
  // Check file type
  const isVideo = /\.(mp4|mov|avi)$/i.test(file.name)
  if (!isVideo) {
    ElMessage.error('Only MP4/MOV/AVI format videos can be uploaded!')
    return false
  }
  
  // Check file size
  const isLt100M = file.size / 1024 / 1024 < 100
  if (!isLt100M) {
    ElMessage.error('Video size cannot exceed 100MB!')
    return false
  }
  
  return true
}

// Handle upload progress
const handleProgress = (event, uploadFile, uploadFiles) => {
  emit('progress', event, uploadFile, uploadFiles)
}

// Handle successful upload
const handleSuccess = (response, uploadFile, uploadFiles) => {
  ElMessage.success('Video uploaded successfully')
  emit('update:modelValue', uploadFiles)
  emit('success', response, uploadFile, uploadFiles)
}

// Handle upload failure
const handleError = (error, uploadFile, uploadFiles) => {
  ElMessage.error('Video upload failed, please try again')
  emit('error', error, uploadFile, uploadFiles)
}

// Remove video
const handleRemove = (uploadFile, uploadFiles) => {
  emit('update:modelValue', uploadFiles ? uploadFiles : fileList.value.filter(item => item !== uploadFile))
  emit('remove', uploadFile, uploadFiles)
}

// Preview video
const handlePreview = (uploadFile) => {
  previewUrl.value = uploadFile.url || uploadFile.response?.url
  previewFile.value = uploadFile
  previewVisible.value = true
}

// Handle exceeding limit
const handleExceed = (files, uploadFiles) => {
  ElMessage.warning(`You can upload a maximum of ${props.limit} videos`)
  emit('exceed', files, uploadFiles)
}

// Format video duration
const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
}
</script>

<style lang="scss" scoped>
.video-upload-container {
  .hide-upload {
    .el-upload {
      display: none;
    }
  }
  
  .upload-icon {
    margin-right: 4px;
  }
  
  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
  
  .video-list {
    margin-top: 16px;
    
    .video-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      margin-bottom: 10px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .video-item-info {
        display: flex;
        align-items: center;
        
        .video-thumbnail {
          position: relative;
          width: 100px;
          height: 56px;
          margin-right: 12px;
          border-radius: 4px;
          overflow: hidden;
          
          .el-image {
            width: 100%;
            height: 100%;
          }
          
          .image-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f5f7fa;
            
            .el-icon {
              font-size: 24px;
              color: #909399;
            }
          }
          
          .video-duration {
            position: absolute;
            right: 4px;
            bottom: 4px;
            padding: 1px 4px;
            font-size: 10px;
            color: #fff;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 2px;
          }
        }
        
        .video-details {
          flex: 1;
          min-width: 0;
          
          .video-name {
            font-size: 14px;
            color: #303133;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 8px;
          }
          
          .video-status {
            width: 200px;
          }
        }
      }
      
      .video-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.video-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  
  .preview-video {
    max-width: 100%;
    max-height: 70vh;
  }
}
</style> 