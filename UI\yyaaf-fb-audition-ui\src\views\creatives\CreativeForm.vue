<template>
  <div class="creative-form-container">
    <div class="page-header">
      <h2>{{ isEdit ? 'Edit Creative' : 'Create Creative' }}</h2>
    </div>

    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="rules" 
      label-width="150px" 
      class="creative-form"
      v-loading="loading"
    >
      <!-- Basic Information -->
      <el-card class="form-section">
        <template #header>
          <div class="card-header">
            <span>Basic Information</span>
          </div>
        </template>
        
        <el-form-item label="Creative Name" prop="name">
          <el-input v-model="formData.name" placeholder="Please enter creative name"></el-input>
        </el-form-item>
        
        <el-form-item label="Creative Type" prop="type">
          <el-radio-group v-model="formData.type" :disabled="isEdit">
            <el-radio label="IMAGE">Image Creative</el-radio>
            <el-radio label="VIDEO">Video Creative</el-radio>
            <!--<el-radio label="CAROUSEL">Carousel Creative</el-radio>-->
          </el-radio-group>
        </el-form-item>
      </el-card>

      <!-- page Information -->
      <el-card class="form-section">
        <template #header>
          <div class="card-header">
            <span>Page Information</span>
          </div>
        </template>
        
        <el-form-item label="Page" prop="pageId">
          <el-select v-model="formData.pageId" placeholder="Please select page" :disabled="isEdit">
            <el-option v-for="item in fbPageOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

      </el-card>

      <!-- Creative Content -->
      <el-card class="form-section">
        <template #header>
          <div class="card-header">
            <span>Creative Content</span>
          </div>
        </template>
        
        <div class="creative-content-container">
          <div class="creative-content-form">
            <!-- Image Creative -->
            <template v-if="formData.type === 'IMAGE'">

              <el-form-item label="Media Library">
                <el-button type="primary" @click="openMediaLibrary('IMAGE')" :disabled="isEdit">
                  Select from Media Library
                </el-button>
              </el-form-item>

              <el-form-item label="Upload Image" prop="imageUrl">
                <el-upload
                  class="creative-upload"
                  :action="''"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleImageChange"
                  :on-remove="handleImageRemove"
                  :file-list="imageFileList"
                  list-type="picture-card"
                  :disabled="isEdit"
                >
                  <el-icon v-if="!formData.imageUrl"><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      Supports JPG, PNG formats, recommended size 1200x628 pixels
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </template>
            
            <!-- Video Creative -->
            <template v-if="formData.type === 'VIDEO'">
              
              <el-form-item label="Media Library">
                <el-button type="primary" @click="openMediaLibrary('VIDEO')" :disabled="isEdit">
                  Select Video from Media Library
                </el-button>
              </el-form-item>

              <el-form-item label="Upload Video" prop="videoUrl">
                <el-upload
                  class="creative-upload"
                  :action="''"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleVideoChange"
                  :on-remove="handleVideoRemove"
                  :file-list="videoFileList"
                  :disabled="isEdit"
                >
                  <el-button>Select Video</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      Supports MP4, WebM formats, recommended duration 15-30 seconds
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
              
              <el-form-item label="Media Library">
                <el-button type="primary" @click="openMediaLibrary('IMAGE')" :disabled="isEdit">
                  Select Image from Media Library
                </el-button>
              </el-form-item>

              <el-form-item label="Upload Image" prop="imageUrl">
                <el-upload
                  class="creative-upload"
                  :action="''"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleImageChange"
                  :on-remove="handleImageRemove"
                  :file-list="imageFileList"
                  list-type="picture-card"
                  :disabled="isEdit"
                >
                  <el-icon v-if="!formData.imageUrl"><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      Supports JPG, PNG formats, recommended size 1200x628 pixels
                    </div>
                  </template>
                </el-upload>
              </el-form-item>

            </template>
            
            <!-- Carousel Creative -->
            <template v-if="formData.type === 'CAROUSEL'">
              <el-form-item label="Upload Carousel Images" prop="carouselImages">
                <el-upload
                  class="creative-upload"
                  :action="''"
                  :auto-upload="false"
                  multiple
                  :limit="10"
                  :on-change="handleCarouselChange"
                  :on-remove="handleCarouselRemove"
                  :file-list="carouselFileList"
                  list-type="picture-card"
                >
                  <el-icon><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      Supports JPG, PNG formats, recommended size 1080x1080 pixels, up to 10 images
                    </div>
                  </template>
                </el-upload>
                <el-button type="primary" size="small" @click="openMediaLibrary('IMAGE')">
                  Select from Media Library
                </el-button>
              </el-form-item>
            </template>
            
            <el-form-item label="Title" prop="title">
              <el-input v-model="formData.title" placeholder="Please enter creative title" :disabled="isEdit"></el-input>
            </el-form-item>
            
            <el-form-item label="Description" prop="message">
              <el-input 
                v-model="formData.message" 
                type="textarea" 
                :rows="4" 
                placeholder="Please enter creative description"
                :disabled="isEdit"
              ></el-input>
            </el-form-item>
            
            <el-form-item label="Call to Action" prop="callToAction">
              <el-select v-model="formData.callToAction" placeholder="Please select call to action button" :disabled="isEdit">
                <el-option-group
                    v-for="group in ctaOptions"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-option-group>
              </el-select>
            </el-form-item>
            
            <el-form-item label="Target Link" prop="linkUrl">
              <el-input v-model="formData.linkUrl" placeholder="Please enter target URL" :disabled="isEdit"></el-input>
            </el-form-item>
          </div>
          
          <!-- Preview -->
          <div class="creative-preview">
            <div class="preview-header">
              <span>Creative Preview</span>
            </div>
            
            <div class="preview-container">
              <!-- Image Creative Preview -->
              <template v-if="formData.type === 'IMAGE' && formData.imageUrl">
                <div class="preview-media">
                  <el-image :src="formData.imageUrl" fit="contain" class="preview-image"></el-image>
                </div>
              </template>
              
              <!-- Video Creative Preview -->
              <template v-if="formData.type === 'VIDEO' && formData.videoUrl">
                <div class="preview-media">
                  <video :src="formData.videoUrl" controls class="preview-video"></video>
                </div>
              </template>
              
              <!-- Carousel Creative Preview -->
              <template v-if="formData.type === 'CAROUSEL' && formData.carouselImages && formData.carouselImages.length > 0">
                <div class="preview-media">
                  <el-carousel height="300px" indicator-position="outside">
                    <el-carousel-item v-for="(image, index) in formData.carouselImages" :key="index">
                      <el-image :src="image.url" fit="contain" class="preview-image"></el-image>
                    </el-carousel-item>
                  </el-carousel>
                </div>
              </template>
              
              <div class="preview-content">
                <h3 v-if="formData.title">{{ formData.title }}</h3>
                <p v-if="formData.message" class="preview-message">{{ formData.message }}</p>
                <p v-if="formData.callToAction" class="preview-cta">
                  <el-tag type="primary">{{ getCallToActionName(formData.callToAction) }}</el-tag>
                </p>
                <p v-if="formData.linkUrl" class="preview-link">
                  <a :href="formData.linkUrl" target="_blank">{{ formData.linkUrl }}</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Form Actions -->
      <div class="form-actions">
        <el-button @click="handleCancel">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? 'Save Changes' : 'Create Creative' }}
        </el-button>
      </div>
    </el-form>

    <!-- Media Selection Dialog -->
    <el-dialog
      v-model="mediaLibraryVisible"
      :title="mediaType === 'IMAGE' ? 'Select Image' : 'Select Video'"
      width="80%"
      destroy-on-close
    >
      <media-selector 
        :initial-type="mediaType"
        @select="handleMediaSelected"
        @cancel="mediaLibraryVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCreativeDetail, createCreative, updateCreative } from '@/api/creative'
import { getFBPageOptions } from '@/api/options'
import { uploadImage, uploadVideo } from '@/api/media'
import MediaSelector from '@/views/creatives/components/MediaSelector.vue'

export default {
  name: 'CreativeForm',
  components: {
    Plus,
    MediaSelector
  },
  data() {
    return {
      // Status
      loading: false,
      submitting: false,
      mediaLibraryVisible: false,
      mediaType: 'IMAGE',
      
      // File upload lists
      imageFileList: [],
      videoFileList: [],
      carouselFileList: [],
      
      // Form data
      formData: {
        id: this.$route.params.id || '',
        name: '',
        type: 'IMAGE',
        pageId: '',
        imageHash: '',
        imageUrl: '',
        videoId: '',
        videoUrl: '',
        carouselImages: [],
        title: '',
        message: '',
        callToAction: '',
        linkUrl: ''
      },
      
      // Form validation rules
      rules: {
        name: [
          { required: true, message: 'Please enter creative name', trigger: 'blur' },
          //{ min: 2, max: 150, message: 'Length should be between 2 and 150 characters', trigger: 'blur' }
        ],
        type: [
          { required: true, message: 'Please select creative type', trigger: 'change' }
        ],
        title: [
          { required: true, message: 'Please enter title', trigger: 'blur' },
          //{ max: 40, message: 'Length cannot exceed 40 characters', trigger: 'blur' }
        ],
        message: [
          { required: true, message: 'Please enter description', trigger: 'blur' },
          //{ max: 125, message: 'Length cannot exceed 125 characters', trigger: 'blur' }
        ],
        linkUrl: [
          { required: true, message: 'Please enter target URL', trigger: 'blur' },
          { pattern: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/, message: 'Please enter a valid URL', trigger: 'blur' }
        ]
      },
      fbPageOptions: [],
      ctaOptions: [
        {
          label: 'Shopping',
          options: [
            { value: 'SHOP_NOW', label: 'Shop Now' },
            { value: 'BUY_NOW', label: 'Buy Now' },
            { value: 'ADD_TO_CART', label: 'Add to Cart' },
            { value: 'VIEW_PRODUCTS', label: 'View Products' }
          ]
        },
        {
          label: 'Conversion',
          options: [
            { value: 'SIGN_UP', label: 'Sign Up' },
            { value: 'DOWNLOAD', label: 'Download' },
            { value: 'LEARN_MORE', label: 'Learn More' },
            { value: 'SUBSCRIBE', label: 'Subscribe' },
            { value: 'APPLY_NOW', label: 'Apply Now' }
          ]
        },
        {
          label: 'Engagement',
          options: [
            { value: 'OPEN_LINK', label: 'Open Link' },
            { value: 'CONTACT_US', label: 'Contact Us' },
            { value: 'MESSAGE_US', label: 'Message Us' },
            { value: 'BOOK_NOW', label: 'Book Now' },
            { value: 'GET_QUOTE', label: 'Get Quote' },
            { value: 'GET_OFFER', label: 'Get Offer' }
          ]
        }
      ]
    }
  },
  computed: {
    isEdit() {
      return !!this.$route.params.id
    }
  },
  methods: {
    // Fetch FB page options
    async fetchFBPageOptions() {
      const result = await getFBPageOptions()
      if(result.code == 0){
        this.fbPageOptions = result.data
      }else{
        ElMessage.error(result.msg)
      }
    },
    // Fetch creative data
    async fetchCreativeData() {
      if (!this.isEdit) return
      
      this.loading = true
      
      try {
        // Should call API to get data in real project
        const result = await getCreativeDetail({id:this.$route.params.id})
        if(result.code == 0){
          // Update form data
          Object.assign(this.formData, result.data)

          this.formData.pageId = result.data.object_story_spec.page_id
          // Update file lists
          if (result.data.type === 'IMAGE') {

            this.formData.imageHash = result.data.object_story_spec.link_data.image_hash
            this.formData.imageUrl = result.data.image_url
            this.formData.message = result.data.object_story_spec.link_data.message
            this.formData.linkUrl = result.data.object_story_spec.link_data.link
            this.formData.callToAction = result.data.object_story_spec.link_data.call_to_action.type
            
            this.imageFileList = [{
              url: result.data.image_url
            }]
          } else if (result.data.type === 'VIDEO') {

            this.formData.videoId = result.data.object_story_spec.video_data.video_id
            this.formData.videoUrl = result.data.object_story_spec.video_data.video_url
            this.formData.imageHash = result.data.object_story_spec.video_data.image_hash
            this.formData.imageUrl = result.data.object_story_spec.video_data.image_url
            this.formData.message = result.data.object_story_spec.video_data.message
            this.formData.linkUrl = result.data.object_story_spec.video_data.call_to_action.value.link
            this.formData.callToAction = result.data.object_story_spec.video_data.call_to_action.type

            this.videoFileList = [{
              name: result.data.object_story_spec.video_data.video_title,
              url: result.data.object_story_spec.video_data.video_url
            }]
            this.imageFileList = [{
              url: result.data.object_story_spec.video_data.image_url
            }]
          }
        }else{
          ElMessage.error(result.msg)
        }
        this.loading = false
      } catch (error) {
        console.error('Failed to fetch creative data:', error)
        ElMessage.error('Failed to fetch creative data')
        this.loading = false
      }
    },
    
    // Image upload handling
    handleImageChange(file, fileList) {
      // Check file type and size
      const isImage = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
      const isLt5M = file.raw.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('Uploaded image can only be JPG or PNG format!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      if (!isLt5M) {
        ElMessage.error('Uploaded image size cannot exceed 5MB!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }

      this.imageFileList = fileList
      this.uploadImage()
    },
    
    handleImageRemove() {
      this.formData.imageUrl = ''
      this.imageFileList = []
    },
    
    // Video upload handling
    handleVideoChange(file, fileList) {
      // Check file type and size
      const isMP4 = file.raw.type === 'video/mp4'
      const isMOV = file.raw.type === 'video/quicktime'
      const isLt10M = file.raw.size / 1024 / 1024 < 10

      if (!isMP4 && !isMOV) {
        ElMessage.error('Video can only be MP4 or MOV format!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      if (!isLt10M) {
        ElMessage.error('Video size cannot exceed 10MB!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }

      this.videoFileList = fileList
      this.uploadVideo()
    },
    
    handleVideoRemove() {
      this.formData.videoUrl = ''
      this.videoFileList = []
    },
 
    // Carousel image upload handling
    handleCarouselChange(file, fileList) {
      // Should upload images to server in real project
      // Simulate local preview
      this.carouselFileList = fileList
      
      this.formData.carouselImages = fileList.map(file => ({
        url: URL.createObjectURL(file.raw),
        name: file.name
      }))
    },
    
    handleCarouselRemove(file, fileList) {
      this.carouselFileList = fileList
      
      this.formData.carouselImages = fileList.map(file => ({
        url: URL.createObjectURL(file.raw),
        name: file.name
      }))
    },
    
    // Open media library selection
    openMediaLibrary(type) {
      this.mediaType = type
      this.mediaLibraryVisible = true
    },
    
    // Handle media selection
    handleMediaSelected(data) {
      const { media, type } = data
      
      if (type === 'IMAGE') {
        this.formData.imageHash = media.hash
        this.formData.imageUrl = media.url
        this.imageFileList = [{
          name: media.name,
          url: media.url
        }]
      } else if (type === 'VIDEO') {
        this.formData.videoId = media.id
        this.formData.videoUrl = media.source
        this.videoFileList = [{
          name: media.title,
          url: media.source
        }]
      }
      
      this.mediaLibraryVisible = false
      ElMessage.success('Material selected from media library')
    },
    async uploadImage() {
      this.loading = true  
      try {
        const formData = new FormData()
        this.imageFileList.forEach(file => {
          formData.append('files[]', file.raw)
        })
        const result = await uploadImage(formData)
        if(result.code == 0){
          this.formData.imageHash = result.data.hash
          this.formData.imageUrl = result.data.url
        }else{
          ElMessage.error(result.msg)
        } 
      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.loading = false
      }
    },
    async uploadVideo() {
      try {
        this.loading = true
        const formData = new FormData()
        const fileName = this.videoFileList[0].name;
        formData.append('file', this.videoFileList[0].raw)
        formData.append('title', fileName)
        const result = await uploadVideo(formData)
        if(result.code == 0){
          this.formData.videoId = result.data.id
          this.formData.videoUrl = result.data.source
          ElMessage.success('Video uploaded successfully')
        }else{
          ElMessage.error(result.msg)
        }
      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.loading = false
      }
    },
    // Form submit
    async handleSubmit() {
      if (!this.$refs.formRef) return
      
      await this.$refs.formRef.validate(async (valid) => {
        if (!valid) {
          ElMessage.warning('Please complete all required fields')
          return
        }
        
        // Type validation
        if (this.formData.type === 'IMAGE' && !this.formData.imageHash) {
          ElMessage.warning('Please upload image')
          return
        }
        
        if (this.formData.type === 'VIDEO' && !this.formData.videoId) {
          ElMessage.warning('Please upload video')
          return
        }
        
        if (this.formData.type === 'CAROUSEL' && (!this.formData.carouselImages || this.formData.carouselImages.length === 0)) {
          ElMessage.warning('Please upload carousel images')
          return
        }
        
        this.submitting = true
        
        try {
          const result = await (this.isEdit ? updateCreative(this.formData.id, this.formData) : createCreative(this.formData))
          if(result.code == 0){
            ElMessage.success(this.isEdit ? 'Creative updated successfully' : 'Creative created successfully')
            this.submitting = false
            this.$router.push('/creatives/list')
          }else{
            ElMessage.error(result.msg)
          }
        } catch (error) {
          console.error('Failed to save creative:', error)
          ElMessage.error('Failed to save creative')
          this.submitting = false
        }
      })
    },
    // Cancel operation
    handleCancel() {
      ElMessageBox.confirm('Are you sure you want to discard the current edit?', 'Tip', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.$router.push('/creatives/list')
      }).catch(() => {})
    },
    // Get call to action button name
    getCallToActionName(cta) {
      const ctaNames = {
        'SHOP_NOW': 'Shop Now',
        'LEARN_MORE': 'Learn More',
        'SIGN_UP': 'Sign Up',
        'DOWNLOAD': 'Download',
        'VIEW_MORE': 'View More',
        'CONTACT_US': 'Contact Us',
        'APPLY_NOW': 'Apply Now',
        'BOOK_NOW': 'Book Now',
        'GET_OFFER': 'Get Offer',
        'TRY_IT': 'Try It Now'
      }
      return ctaNames[cta] || cta
    }
  },
  mounted() {
    this.fetchFBPageOptions()
    this.fetchCreativeData()
  }
}
</script>

<style scoped>
.creative-form-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.form-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.creative-upload, .thumbnail-upload {
  margin-bottom: 10px;
}

.form-actions {
  display: flex;
  justify-content: right;
  margin-top: 10px;
}

.creative-content-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.creative-content-form {
  width: 60%;
}

.creative-preview {
  width: 40%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f5f7fa;
}

.preview-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
}

.preview-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.preview-media {
  width: 100%;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
}

.preview-image {
  max-width: 100%;
  max-height: 250px;
}

.preview-video {
  max-width: 100%;
  max-height: 250px;
}

.preview-content {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.preview-message {
  margin: 10px 0;
  color: #606266;
}

.preview-cta {
  margin: 10px 0;
}

.preview-link {
  margin: 10px 0;
  word-break: break-all;
}

.preview-link a {
  color: #409eff;
  text-decoration: none;
}

.preview-link a:hover {
  text-decoration: underline;
}
</style> 