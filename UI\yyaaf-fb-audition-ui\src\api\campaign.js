import request from '@/utils/request'

/**
 * 获取广告系列列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getCampaignList(params) {
  return request({
    url: '/campaigns',
    method: 'get',
    params
  })
}

// 同步广告系列
export function getSyncCampaigns(params) {
  return request({
    url: '/campaigns/sync',
    method: 'get',
    params
  })
}

/**
 * 获取广告系列详情
 * @param {string|number} id 广告系列ID
 * @returns {Promise}
 */
export function getCampaignDetail(id) {
  return request({
    url: `/campaigns/${id}`,
    method: 'get'
  })
}

/**
 * 创建广告系列
 * @param {Object} data 广告系列数据
 * @returns {Promise}
 */
export function createCampaign(data) {
  return request({
    url: '/campaigns/create',
    method: 'post',
    data
  })
}

/**
 * 更新广告系列
 * @param {string|number} id 广告系列ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export function updateCampaign(id, data) {
  return request({
    url: `/campaigns/update/${id}`,
    method: 'post',
    data
  })
}

/**
 * 删除广告系列
 * @param {string|number} id 广告系列ID
 * @returns {Promise}
 */
export function deleteCampaign(data) {
  return request({
    url: `/campaigns/delete`,
    method: 'post',
    data
  })
}

// 修改广告系列状态
export function updateCampaignStatus(id, status) {
  return request({
    url: `/campaigns/status/${id}`,
    method: 'post',
    data: { status }
  })
} 

// 获取广告系列选项
export function getCampaignOptions(params) {
  return request({
    url: '/campaigns/options',
    method: 'get',
    params
  })
}

/**
 * 获取广告系列投放目标选项
 * @returns {Promise}
 */
export function getCampaignObjectives() {
  return request({
    url: '/campaigns/objectives',
    method: 'get'
  })
}

/**
 * 获取广告系列统计数据
 * @param {Object} params 查询参数 
 * @returns {Promise}
 */
export function getCampaignStats(params) {
  return request({
    url: '/campaigns/stats',
    method: 'get',
    params
  })
}

/**
 * 获取广告系列详情统计数据
 * @param {string|number} id 广告系列ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getCampaignDetailStats(id, params) {
  return request({
    url: `/campaigns/${id}/stats`,
    method: 'get',
    params
  })
}

/**
 * 复制现有广告系列
 * @param {string|number} id 广告系列ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export function duplicateCampaign(id, data = {}) {
  return request({
    url: `/campaigns/${id}/duplicate`,
    method: 'post',
    data
  })
}

// 获取购买类型选项
export function getBuyingTypes() {
  return request({
    url: '/buying-types',
    method: 'get'
  })
}

// 获取特殊广告类别
export function getSpecialAdCategories() {
  return request({
    url: '/special-ad-categories',
    method: 'get'
  })
}