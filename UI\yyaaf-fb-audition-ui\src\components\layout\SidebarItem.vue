<template>
  <div v-if="!item.meta?.hidden && !item.hidden">
    <!-- 没有子菜单的情况 -->
    <template v-if="!hasOneShowingChild(item.children, item) && item.children">
      <el-sub-menu :index="resolvePath(item.path)">
        <template #title>
          <el-icon v-if="item.meta && item.meta.icon"><component :is="item.meta.icon" /></el-icon>
          <span>{{ item.meta.title }}</span>
        </template>
        
        <sidebar-item
          v-for="child in item.children"
          :key="child.path"
          :item="child"
          :base-path="resolvePath(child.path)"
        />
      </el-sub-menu>
    </template>
    
    <!-- 有且只有一个可显示子菜单的情况 -->
    <template v-else>
      <el-menu-item :index="resolvePath(onlyOneChild.path)" @click="navigateTo(onlyOneChild)">
        <el-icon v-if="(onlyOneChild.meta && onlyOneChild.meta.icon) || (item.meta && item.meta.icon)">
          <component :is="(onlyOneChild.meta && onlyOneChild.meta.icon) || (item.meta && item.meta.icon)" />
        </el-icon>
        <template #title>{{ onlyOneChild.meta ? onlyOneChild.meta.title : (item.meta ? item.meta.title : '') }}</template>
      </el-menu-item>
    </template>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { isExternal } from '@/utils/validate'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

const router = useRouter()
const onlyOneChild = ref(null)

// 判断是否有且只有一个可显示的子菜单
const hasOneShowingChild = (children = [], parent) => {
  if (!children) {
    children = []
  }
  
  const showingChildren = children.filter(item => {
    if (item.hidden || (item.meta && item.meta.hidden)) {
      return false
    } else {
      // 设置临时变量
      onlyOneChild.value = item
      return true
    }
  })
  
  // 当只有一个子路由时，默认显示子路由
  if (showingChildren.length === 1) {
    return true
  }
  
  // 没有子路由则显示父路由
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }
  
  return false
}

// 解析路径
const resolvePath = (routePath) => {
  if (isExternal(routePath)) {
    return routePath
  }
  
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  
  if (routePath.startsWith('/')) {
    return routePath
  }
  
  return props.basePath + '/' + routePath
}

// 导航到路由
const navigateTo = (route) => {
  if (route.noShowingChildren) return
  
  const path = resolvePath(route.path)
  if (isExternal(path)) {
    window.open(path, '_blank')
  } else {
    console.log('Navigating to:', path)
    router.push(path).catch(err => {
      console.error('Navigation error:', err)
    })
  }
}
</script> 