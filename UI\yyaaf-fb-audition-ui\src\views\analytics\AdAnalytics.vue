<template>
  <div class="analytics-container">
    <div class="header-section">
      <h2>Ad Analytics</h2>
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="Date Range">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="To"
              start-placeholder="Start Date"
              end-placeholder="End Date"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item label="Campaign">
            <el-select
              v-model="filterForm.campaignId"
              placeholder="Select Campaign"
              filterable
              clearable
              @change="fetchAdSetOptions"
            >
              <el-option
                v-for="item in campaignOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Ad Set">
            <el-select
              v-model="filterForm.adsetId"
              placeholder="Select Ad Set"
              filterable
              clearable
              :disabled="!filterForm.campaignId"
              @change="fetchAdOptions"
            >
              <el-option
                v-for="item in adsetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Ad">
            <el-select
              v-model="filterForm.adIds"
              multipl
              collapse-tags
              placeholder="Select Ad"
              filterable
              clearable
              :disabled="!filterForm.adsetId"
            >
              <el-option
                v-for="item in adOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!--<el-form-item label="Ad Type">
            <el-select
              v-model="filterForm.adType"
              placeholder="Select Ad Type"
              clearable
            >
              <el-option label="Image Ad" value="IMAGE" />
              <el-option label="Video Ad" value="VIDEO" />
              <el-option label="Carousel Ad" value="CAROUSEL" />
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-button type="primary" @click="fetchAnalyticsData">Query</el-button>
            <el-button @click="resetFilters">Reset</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(card, index) in overviewCards" :key="index">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">{{ card.value }}</div>
              <div class="card-trend" :class="card.trend > 0 ? 'positive' : card.trend < 0 ? 'negative' : ''">
                <span>{{ card.trend > 0 ? '+' : '' }}{{ card.trend }}%</span>
                <el-icon v-if="card.trend > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="card.trend < 0"><ArrowDown /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="chart-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Ad Performance Trend</span>
                <el-radio-group v-model="trendMetric" size="small">
                  <el-radio-button label="impressions">Impressions</el-radio-button>
                  <el-radio-button label="clicks">Clicks</el-radio-button>
                  <el-radio-button label="conversions">Conversions</el-radio-button>
                  <el-radio-button label="spend">Spend</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container" ref="trendChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="chart-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Ad Performance Comparison</span>
                <el-select v-model="adMetric" size="small" style="width: 120px">
                  <el-option label="CTR" value="ctr" />
                  <el-option label="Conversion Rate" value="conversionRate" />
                  <el-option label="Avg. CPC" value="cpc" />
                  <el-option label="Avg. CPM" value="cpm" />
                </el-select>
              </div>
            </template>
            <div class="chart-container" ref="adChartRef"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Previous Period Comparison</span>
                <el-select v-model="comparisonMetric" size="small" style="width: 120px">
                  <el-option label="Impressions" value="impressions" />
                  <el-option label="Clicks" value="clicks" />
                  <el-option label="Conversions" value="conversions" />
                  <el-option label="Spend" value="spend" />
                </el-select>
              </div>
            </template>
            <div class="chart-container" ref="comparisonChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="detail-section">
      <el-card shadow="hover">
        <template #header>
          <div class="detail-header">
            <span>Ad Performance Details</span>
            <!--<div>
              <el-button type="primary" size="small" @click="previewAd" :disabled="!selectedAd">Preview Ad</el-button>
              <el-button type="primary" size="small" @click="exportData">Export Data</el-button>
            </div>-->
          </div>
        </template>
        <table-pagination 
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :table-data="adData" 
          :loading="loading"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="ad_name" label="Ad Name" min-width="200" />
          <el-table-column prop="adset_name" label="Ad Set" min-width="300" />
          <el-table-column prop="adcreative_type" label="Creative Type" min-width="120">
            <template #default="scope">
              {{ getCreativeTypeName(scope.row.adcreative_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="Status" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="impressions" label="Impressions" sortable min-width="150"/>
          <el-table-column prop="clicks" label="Clicks" sortable  min-width="120"/>
          <el-table-column prop="ctr" label="CTR" sortable>
            <template #default="scope">
              {{ (scope.row.ctr).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column prop="conversions" label="Conversions" sortable min-width="150"/>
          <el-table-column prop="conversionRate" label="Conversion Rate" sortable min-width="180">
            <template #default="scope">
              {{ (scope.row.conversionRate).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column prop="spend" label="Spend" sortable min-width="100">
            <template #default="scope">
              ¥{{ scope.row.spend.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="cpc" label="Avg. CPC" sortable min-width="120">
            <template #default="scope">
              ¥{{ scope.row.cpc.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="frequency" label="Frequency" sortable min-width="120" />
          <el-table-column fixed="right" label="Actions" width="200">
            <template #default="scope">
              <el-button type="text" size="small" @click.stop="viewAdDetail(scope.row.ad_id)">
                View Details
              </el-button>
              <el-button type="text" size="small" @click.stop="editAd(scope.row.ad_id)">
                Edit
              </el-button>
            </template>
          </el-table-column>
        </table-pagination>
      </el-card>
    </div>

    <!-- Ad Preview Dialog -->
    <el-dialog
      v-model="previewDialogVisible"
      title="Ad Preview"
      width="600px"
      class="ad-preview-dialog"
    >
      <div class="ad-preview" v-if="selectedAd">
        <div class="ad-preview-container">
          <div v-if="selectedAd.creativeType === 'IMAGE'" class="image-preview">
            <img :src="selectedAd.creativeUrl || 'https://via.placeholder.com/500x300'" alt="Ad Image" />
          </div>
          <div v-else-if="selectedAd.creativeType === 'VIDEO'" class="video-preview">
            <video controls :src="selectedAd.creativeUrl || ''" width="100%"></video>
          </div>
          <div v-else-if="selectedAd.creativeType === 'CAROUSEL'" class="carousel-preview">
            <el-carousel :interval="4000" type="card" height="200px">
              <el-carousel-item v-for="(item, index) in selectedAd.carouselItems || []" :key="index">
                <img :src="item.imageUrl || 'https://via.placeholder.com/500x300'" alt="Carousel Image" />
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
        <div class="ad-info">
          <h3>{{ selectedAd.name }}</h3>
          <p class="ad-headline">{{ selectedAd.headline }}</p>
          <p class="ad-description">{{ selectedAd.description }}</p>
          <div class="ad-cta">
            <el-button type="primary" size="small">{{ selectedAd.callToAction || 'Learn More' }}</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { getCampaignOptions } from '@/api/campaign'
import { getAdsetOptions } from '@/api/adset'
import { getAdOptions } from '@/api/ad'
import { adinsights, overview, trends, adcompare, previouscomparison } from '@/api/adanalytics'
import * as echarts from 'echarts'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'AdAnalytics',
  components: {
    ArrowUp,
    ArrowDown,
    TablePagination
  },
  data() {
    return {
      loading: false,
      // Loading status for each module
      loadingStates: {
        insights: false,
        overview: false,
        trends: false,
        adcompare: false,
        previouscomparison: false
      },
      campaignOptions: [],
      adsetOptions: [],
      adOptions: [],
      adData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      selectedAd: null,
      previewDialogVisible: false,
      filterForm: {
        dateRange: [
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
          new Date().toISOString().substr(0, 10)
        ],
        campaignId: '',
        adsetId: '',
        adIds: [],
        adType: ''
      },
      trendMetric: 'impressions',
      adMetric: 'ctr',
      comparisonMetric: 'impressions',
      overviewCards: [
        { title: 'Total Impressions', value: '0', trend: 0 },
        { title: 'Total Clicks', value: '0', trend: 0 },
        { title: 'Total Conversions', value: '0', trend: 0 },
        { title: 'Total Spend', value: '$0.00', trend: 0 }
      ],
      
      // Chart instances
      trendChart: null,
      adChart: null,
      comparisonChart: null,
      
      // Referenced elements
      trendChartRef: null,
      adChartRef: null,
      comparisonChartRef: null
    }
  },
  
  // Lifecycle hooks
  mounted() {
    this.router = useRouter()
    this.fetchCampaignOptions()
    this.fetchAnalyticsData()
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
    if (this.trendChart) this.trendChart.dispose()
    if (this.adChart) this.adChart.dispose()
    if (this.comparisonChart) this.comparisonChart.dispose()
  },
  
  // Listeners
  watch: {
    trendMetric: {
      async handler() {
        try {
          const params = {
            beginTime: this.filterForm.dateRange[0],
            endTime: this.filterForm.dateRange[1],
            campaignId: this.filterForm.campaignId || undefined,
            adsetId: this.filterForm.adsetId || undefined,
            adIds: this.filterForm.adIds.length > 0 ? this.filterForm.adIds : undefined,
            trendMetric: this.trendMetric
          }
          
          const response = await trends(params)
          if (response.code === 0) {
            this.trendsData = response.data || {}
            this.renderTrendChart(this.trendsData)
          }
        } catch (error) {
          console.error('Failed to get trend data:', error)
          ElMessage.error('Failed to get trend data')
        }
      }
    },
    adMetric: {
      async handler() {
        try {
          const params = {
            beginTime: this.filterForm.dateRange[0],
            endTime: this.filterForm.dateRange[1],
            campaignId: this.filterForm.campaignId || undefined,
            adsetId: this.filterForm.adsetId || undefined,
            adIds: this.filterForm.adIds.length > 0 ? this.filterForm.adIds : undefined
          }
          
          const response = await adcompare(params)
          if (response.code === 0) {
            this.renderAdChart(response.data || [])
          }
        } catch (error) {
          console.error('Failed to get ad comparison data:', error)
          ElMessage.error('Failed to get ad comparison data')
        }
      }
    },
    comparisonMetric: {
      async handler() {
        try {
          const params = {
            beginTime: this.filterForm.dateRange[0],
            endTime: this.filterForm.dateRange[1],
            campaignId: this.filterForm.campaignId || undefined,
            adsetId: this.filterForm.adsetId || undefined,
            adIds: this.filterForm.adIds.length > 0 ? this.filterForm.adIds : undefined
          }
          
          const response = await previouscomparison(params)
          if (response.code === 0) {
            this.renderComparisonChart(response.data || {})
          }
        } catch (error) {
          console.error('Failed to get period comparison data:', error)
          ElMessage.error('Failed to get period comparison data')
        }
      }
    }
  },
  
  // Methods
  methods: {
    // Get campaign options
    async fetchCampaignOptions() {
      try {
        const response = await getCampaignOptions()
        if(response.code === 0){
          this.campaignOptions = response.data || []
        }
      } catch (error) {
        console.error('Failed to get campaign options:', error)
        ElMessage.error('Failed to get campaign options')
      }
    },
    
    // Get ad set options
    async fetchAdSetOptions() {
      try {
        if (!this.filterForm.campaignId) {
          this.adsetOptions = []
          this.filterForm.adsetId = ''
          this.filterForm.adIds = ''
          return
        }
        
        const response = await getAdsetOptions(this.filterForm.campaignId)
        if(response.code === 0 ){
          this.adsetOptions = response.data || []
        }
      } catch (error) {
        console.error('Failed to get ad set options:', error)
        ElMessage.error('Failed to get ad set options')
      }
    },
    
    // Get ad options
    async fetchAdOptions() {
      try {
        if (!this.filterForm.adsetId) {
          this.adOptions = []
          this.filterForm.adIds = ''
          return
        }
        
        const response = await getAdOptions(this.filterForm.adsetId)
        if(response.code === 0 ){
          this.adOptions = response.data || []
        }
      } catch (error) {
        console.error('Failed to get ad options:', error)
        ElMessage.error('Failed to get ad options')
      }
    },
    
    // Get analytics data
    async fetchAnalyticsData() {
      this.loading = true
      try {
        const params = {
          beginTime: this.filterForm.dateRange[0],
          endTime: this.filterForm.dateRange[1],
          campaignId: this.filterForm.campaignId || undefined,
          adsetId: this.filterForm.adsetId || undefined,
          adIds: this.filterForm.adIds.length > 0 ? this.filterForm.adIds : undefined,
          page: this.currentPage,
          limit: this.pageSize,
          trendMetric: this.trendMetric
        }

        // 1. Get data insights
        this.loadingStates.insights = true
        const insightsRes = await adinsights(params)
        if (insightsRes.code === 0) {
          this.adData = insightsRes.data || []
          this.total = insightsRes.total || 0
        }
        this.loadingStates.insights = false

        // 2. Get overview data
        this.loadingStates.overview = true
        const overviewRes = await overview(params)
        if (overviewRes.code === 0) {
          this.updateOverviewCards(overviewRes.data || {})
        }
        this.loadingStates.overview = false

        // 3. Get trend data
        this.loadingStates.trends = true
        const trendsRes = await trends(params)
        if (trendsRes.code === 0) {
          this.trendsData = trendsRes.data || {}
        }
        this.loadingStates.trends = false

        // 4. Get ad comparison data
        this.loadingStates.adcompare = true
        const adcompareRes = await adcompare(params)
        this.loadingStates.adcompare = false

        // 5. Get period comparison data
        this.loadingStates.previouscomparison = true
        const previouscomparisonRes = await previouscomparison(params)
        this.loadingStates.previouscomparison = false

        // Render charts
        this.$nextTick(() => {
          this.initCharts()
          
          // Render trend chart
          if (trendsRes.code === 0) {
            this.renderTrendChart(this.trendsData)
          }
          
          // Render ad comparison chart
          if (adcompareRes.code === 0) {
            this.renderAdChart(adcompareRes.data || [])
          }

          // Render period comparison chart
          if (previouscomparisonRes.code === 0) {
            this.renderComparisonChart(previouscomparisonRes.data || {})
          }
        })
      } catch (error) {
        console.error('Failed to get analytics data:', error)
        ElMessage.error('Failed to get analytics data')
      } finally {
        this.loading = false
        // Reset all loading states
        Object.keys(this.loadingStates).forEach(key => {
          this.loadingStates[key] = false
        })
      }
    },
    
    // Update overview card data
    updateOverviewCards(overview) {
      if (!overview) return
     
      this.overviewCards = [
        { 
          title: 'Total Impressions', 
          value: this.formatNumber(overview.impressions), 
          trend: overview.impressionsTrend 
        },
        { 
          title: 'Total Clicks', 
          value: this.formatNumber(overview.clicks), 
          trend: overview.clicksTrend 
        },
        { 
          title: 'Total Conversions', 
          value: this.formatNumber(overview.conversions), 
          trend: overview.conversionsTrend 
        },
        { 
          title: 'Total Spend', 
          value: `$${this.formatNumber(overview.todaySpend)}`, 
          trend: overview.todaySpendTrend 
        }
      ]
    },
    
    // Initialize charts
    initCharts() {
      // Destroy existing chart instances
      if (this.trendChart) this.trendChart.dispose()
      if (this.adChart) this.adChart.dispose()
      if (this.comparisonChart) this.comparisonChart.dispose()
      
      // Create new chart instances
      this.trendChart = echarts.init(this.$refs.trendChartRef)
      this.adChart = echarts.init(this.$refs.adChartRef)
      this.comparisonChart = echarts.init(this.$refs.comparisonChartRef)
    },
    
    // Render trend chart
    renderTrendChart(trends = {}) {
      if (!this.trendChart) return
      
      const dates = trends?.xAxis || []
      const values = trends?.series || []
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: this.getMetricName(this.trendMetric),
            type: 'line',
            smooth: true,
            data: values,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.7)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0.1)'
                  }
                ]
              }
            }
          }
        ]
      }
      
      this.trendChart.setOption(option)
    },
    
    // Render creative performance chart
    renderAdChart(data = []) {
      if (!this.adChart) return
      
      // Sort by metric value
      data.sort((a, b) => b[this.adMetric] - a[this.adMetric])
      
      // Display top 10 at most
      const displayData = data
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            const data = params[0].data
            if (this.adMetric === 'ctr' || this.adMetric === 'conversionRate') {
              return `${params[0].name}: ${data.toFixed(2)}%`
            }
            return `${params[0].name}: ${data}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => {
              if (this.adMetric === 'ctr' || this.adMetric === 'conversionRate') {
                return value.toFixed(0) + '%'
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'category',
          data: displayData.map(item => item.name),
          axisLabel: {
            formatter: (value) => {
              if (value.length > 12) {
                return value.substring(0, 12) + '...'
              }
              return value
            }
          }
        },
        series: [
          {
            name: this.getAdMetricName(this.adMetric),
            type: 'bar',
            data: displayData.map(item => item[this.adMetric]),
            itemStyle: {
              color: (params) => {
                // Gradient color based on value size
                const colorList = ['#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
                return colorList[params.dataIndex % colorList.length]
              }
            },
            label: {
              show: true,
              position: 'right',
              formatter: (params) => {
                if (this.adMetric === 'ctr' || this.adMetric === 'conversionRate' || this.adMetric === 'engagementRate') {
                  return params.value.toFixed(2) + '%'
                }
                else if (this.adMetric === 'cpc' || this.adMetric === 'cpm') {
                  return `${params.value.toFixed(2)}$`
                }
                return params.value
              }
            }
          }
        ]
      }
      
      this.adChart.setOption(option)
    },
    
    // Render comparison chart
    renderComparisonChart(data = {}) {
      if (!this.comparisonChart) return
      
      const currentPeriod = data.current || []
      const previousPeriod = data.previous || []
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['Current Period', 'Previous Period']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: currentPeriod.map(item => item.date)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'Current Period',
            type: 'line',
            data: currentPeriod.map(item => item[this.comparisonMetric]),
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: 'Previous Period',
            type: 'line',
            data: previousPeriod.map(item => item[this.comparisonMetric]),
            itemStyle: {
              color: '#909399'
            }
          }
        ]
      }
      
      this.comparisonChart.setOption(option)
    },
    
    // Handle window resize
    handleResize() {
      this.trendChart && this.trendChart.resize()
      this.adChart && this.adChart.resize()
      this.comparisonChart && this.comparisonChart.resize()
    },
    
    // Handle date change
    handleDateChange() {
      this.fetchAnalyticsData()
    },
    
    // Handle page size change
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchAnalyticsData()
    },
    
    // Handle current page change
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchAnalyticsData()
    },
    
    // Row click handler
    handleRowClick(row) {
      this.selectedAd = row
    },
    
    // Preview ad
    previewAd() {
      if (!this.selectedAd) {
        ElMessage.warning('Please select an ad first')
        return
      }
      this.previewDialogVisible = true
    },
    
    // Preview ad from table
    previewAdFromTable(row) {
      this.selectedAd = row
      this.previewDialogVisible = true
    },
    
    // View ad details
    viewAdDetail(id) {
     /* this.$router.push({
        path: `/analytics/ad-detail/${id}`,
        query: {
          startDate: this.filterForm.dateRange[0],
          endDate: this.filterForm.dateRange[1]
        }
      })*/
      this.$router.push({
        path: '/ads',
        query: { id: id }
      })
    },

    editAd(id) {
      this.$router.push({
        path: `/ads/edit/${id}`
      })
    },
    
    // Reset filters
    resetFilters() {
      this.filterForm = {
        dateRange: [
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
          new Date().toISOString().substr(0, 10)
        ],
        campaignId: '',
        adsetId: '',
        adId: '',
        adType: ''
      }
      this.adsetOptions = []
      this.adOptions = []
      this.fetchAnalyticsData()
    },
    
    // Export data
    exportData() {
      ElMessage.success('Data exported')
    },
    
    // Helper function - Format number
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },
    
    // Helper function - Get metric name
    getMetricName(metric) {
      const metricNames = {
        impressions: 'Impressions',
        clicks: 'Clicks',
        conversions: 'Conversions',
        spend: 'Spend'
      }
      return metricNames[metric] || metric
    },
    
    // Helper function - Get creative metric name
    getAdMetricName(metric) {
      const metricNames = {
        ctr: 'CTR',
        conversionRate: 'Conversion Rate',
        engagementRate: 'Engagement Rate'
      }
      return metricNames[metric] || metric
    },
    
    // Helper function - Get creative type name
    getCreativeTypeName(type) {
      const typeNames = {
        IMAGE: 'Image Ad',
        VIDEO: 'Video Ad',
        CAROUSEL: 'Carousel Ad'
      }
      return typeNames[type] || type
    },

    getStatusName(status) {
      const statusNames = {
        ACTIVE: 'Active',
        PAUSED: 'Paused',
        ARCHIVED: 'Archived',
        DELETED: 'Deleted'
      }
      return statusNames[status] || status
    },

    getStatusType(status) {
      const statusTypes = {
        ACTIVE: 'success',
        PAUSED: 'warning',
        ARCHIVED: 'info',
        DELETED: 'danger'
      }
      return statusTypes[status] || ''
    }
  }
}
</script>

<style scoped>
.analytics-container {
  padding: 20px;
}

.header-section {
  margin-bottom: 20px;
}

.filter-section {
  margin-top: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.card-title {
  font-size: 14px;
  color: #606266;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin: 8px 0;
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.card-trend.positive {
  color: #67c23a;
}

.card-trend.negative {
  color: #f56c6c;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header, .detail-header, .time-comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.detail-section, .time-comparison-section {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.ad-preview {
  padding: 10px;
}

.ad-preview-container {
  margin-bottom: 20px;
  text-align: center;
}

.image-preview img,
.carousel-preview img {
  max-width: 100%;
  border-radius: 8px;
}

.ad-info {
  padding: 10px;
  border-top: 1px solid #ebeef5;
}

.ad-headline {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.ad-description {
  color: #606266;
  margin-bottom: 15px;
}

.ad-cta {
  text-align: center;
  margin-top: 10px;
}
</style> 