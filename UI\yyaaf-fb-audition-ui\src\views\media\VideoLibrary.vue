<template>
  <div class="video-library">
    <!-- Search Filter Area -->
    <div class="filter-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="Material Name">
          <el-input
            v-model="searchForm.title"
            placeholder="Please enter material name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            Search
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            Reset
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Batch Operations Area -->
    <div class="batch-actions">
      <div class="left-actions">
        <el-button type="primary" @click="dialogVisible.upload = true">
          <el-icon><Upload /></el-icon>
          Upload Video
        </el-button>
      </div>
      <div class="right-actions" v-if="selectedVideos.length > 0">
        <el-button size="small" type="danger" @click="batchDelete">
          <el-icon><Delete /></el-icon>
          Batch Delete ({{ selectedVideos.length }})
        </el-button>
      </div>
    </div>

    <!-- Video List -->
    <div class="video-grid">
      <el-empty v-if="loading" description="Loading...">
        <template #image>
          <el-icon class="loading-icon"><Loading /></el-icon>
        </template>
      </el-empty>
      
      <el-empty v-else-if="videos.length === 0" description="No video materials yet" />
      
      <template v-else>
        <div
          v-for="video in videos"
          :key="video.id"
          class="video-item"
          :class="{ selected: selectedVideos.includes(video) }"
          @click="toggleSelectVideo(video.id)"
        >
          <div class="video-wrapper">
            <div class="video-thumbnail" @click.stop="previewVideo(video)">
              <el-image
                :src="video.picture"
                fit="cover"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><VideoPlay /></el-icon>
                    <span>No Thumbnail</span>
                  </div>
                </template>
              </el-image>
              <div class="video-duration">{{ formatDuration(video.length) }}</div>
              <div class="video-play-icon">
                <el-icon><VideoPlay /></el-icon>
              </div>
            </div>
            <div class="video-checkbox" @click.stop="toggleSelectVideo(video.id)">
              <el-checkbox v-model="video.selected" :label="video.title" />
            </div>
          </div>
          <div class="video-info">
            <div class="video-title" :title="video.title">{{ video.title }}</div>
            <div class="video-date">{{ formatDate(video.created_time) }}</div>
          </div>
          <div class="video-actions">
            <el-button
              type="primary"
              size="small"
              text
              @click.stop="toggleSelectVideo(video.id)"
            >
              Select
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click.stop="deleteVideo(video)"
            >
              Delete
            </el-button>
          </div>
        </div>
      </template>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
      <el-button 
        :disabled="!pagination.hasPrevious" 
        @click="loadPreviousPage"
        icon="ArrowLeft"
      >
        Previous
      </el-button>
      <el-button 
        :disabled="!pagination.hasNext" 
        @click="loadNextPage"
        icon="ArrowRight"
      >
        Next
      </el-button>
    </div>

    <!-- Video Preview Dialog -->
    <el-dialog
      v-model="dialogVisible.preview"
      title="Video Preview"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentVideo" class="preview-container">
        <div class="preview-video">
          <video
            ref="videoPlayer"
            :src="currentVideo.source"
            controls
            controlsList="nodownload"
            class="video-player"
          ></video>
        </div>
        <div class="preview-info">
          <p><strong>Name:</strong>{{ currentVideo.title }}</p>
          <p><strong>Duration:</strong>{{ formatDuration(currentVideo.length) }}</p>
          <p><strong>Creation Time:</strong>{{ formatDate(currentVideo.created_time) }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="danger" @click="deleteCurrentVideo">Delete</el-button>
          <el-button @click="closePreviewDialog">Close</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Add Upload Video Dialog -->
    <el-dialog
      v-model="dialogVisible.upload"
      title="Upload Video"
      width="600px"
      destroy-on-close
    >
      <el-form :model="uploadForm" label-width="80px" class="upload-form">
        <el-form-item label="Name" prop="title">
          <el-input v-model="uploadForm.title" placeholder="Please enter video name" />
        </el-form-item>
        <el-form-item label="Description">
          <el-input 
            v-model="uploadForm.description" 
            type="textarea" 
            rows="3" 
            placeholder="Please enter video description (optional)" 
          />
        </el-form-item>
      </el-form>

      <el-upload
        class="video-uploader"
        :action="''"
        :auto-upload="false"
        :multiple="false"
        :limit="1"
        :on-change="handleUploadChange"
        :on-remove="handleUploadRemove"
        :file-list="uploadFiles"
        :before-upload="beforeVideoUpload"
      >
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          Select Video
        </el-button>
        <template #tip>
          <div class="el-upload__tip">
            Supports MP4, WebM format, single file does not exceed 10MB
          </div>
        </template>
      </el-upload>

      <div class="upload-progress" v-if="uploading">
        <el-progress :percentage="uploadProgress" :format="progressFormat" />
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.upload = false" :disabled="uploading">Cancel</el-button>
          <el-button type="primary" :loading="uploading" @click="submitUpload" :disabled="uploadFiles.length === 0">
            {{ uploading ? 'Uploading...' : 'Start Upload' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Search, Refresh, Delete, Loading, VideoPlay, Upload, Plus} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getVideoList, 
  deleteVideo as apiDeleteVideo,
  uploadVideo,
  uploadVideoThumbnail
} from '@/api/media'

export default {
  name: 'VideoLibrary',
  components: {
    Search,
    Refresh,
    Delete,
    Loading,
    VideoPlay,
    Upload,
    Plus,
  },
  data() {
    return {
      // Search form
      searchForm: {
        title: ''
      },
      
      // Table loading status
      loading: false,
      
      // Video list
      videos: [],
      
      // Pagination information
      pagination: {
        after: '',
        before: '',
        hasNext: false,
        hasPrevious: false
      },
      
      // Dialog visible status
      dialogVisible: {
        preview: false,
        upload: false
      },
      
      // Current operating video
      currentVideo: null,
      
      // Upload related
      uploadFiles: [],
      uploadForm: {
        title: '',
        description: ''
      },
      uploading: false,
      uploadProgress: 0
    }
  },
  computed: {
    // Selected video ID
    selectedVideos() {
      return this.videos
        .filter(item => item.selected)
        .map(item => item)
    }
  },
  methods: {
    // Define refresh function
    refreshLibrary() {
      this.pagination.after = ''
      this.pagination.before = ''
      this.fetchVideos()
    },
    
    // Get video list
    async fetchVideos() {
      this.loading = true
      
      try {
        // Build query parameters
        const params = {
          limit: 24
        }

        // Add Facebook API pagination parameters
        if (this.pagination.after) {
          params.after = this.pagination.after
        }

        if (this.pagination.before) {
          params.before = this.pagination.before
        }

        // Add search criteria
        if (this.searchForm.title) {
          params.title = this.searchForm.title
        }

        // Call API to get video list
        const res = await getVideoList(params)
        
        // Process video data
        this.videos = res.data.map(item => ({
          ...item,
          selected: false
        }))
        
        // Update pagination information
        this.pagination.after = res.paging?.cursors?.after || ''
        this.pagination.before = res.paging?.cursors?.before || ''
        this.pagination.hasNext = !!res.paging?.next
        this.pagination.hasPrevious = !!res.paging?.previous
      } catch (error) {
        console.error('Failed to get video list', error)
        ElMessage.error('Failed to get video list')
      } finally {
        this.loading = false
      }
    },
    
    // Search
    handleSearch() {
      this.pagination.after = ''
      this.pagination.before = ''
      this.fetchVideos()
    },
    
    // Reset search criteria
    resetSearch() {
      Object.keys(this.searchForm).forEach(key => {
        if (Array.isArray(this.searchForm[key])) {
          this.searchForm[key] = []
        } else {
          this.searchForm[key] = ''
        }
      })
      this.handleSearch()
    },
    
    // Load previous page
    loadPreviousPage() {
      if (this.pagination.hasPrevious) {
        this.pagination.after = ''
        this.fetchVideos()
      }
    },
    
    // Load next page
    loadNextPage() {
      if (this.pagination.hasNext) {
        this.pagination.before = ''
        this.fetchVideos()
      }
    },
    
    // Toggle selected video
    toggleSelectVideo(id) {
      const video = this.videos.find(item => item.id === id)
      if (video) {
        video.selected = !video.selected
      }
    },
    
    // Preview video
    previewVideo(video) {
      this.currentVideo = video
      this.dialogVisible.preview = true
    },
    
    // Close preview dialog
    closePreviewDialog() {
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
      }
      this.dialogVisible.preview = false
    },
    
    // Delete currently previewed image
    deleteCurrentVideo() {
      if (this.currentVideo) {
        this.deleteVideo(this.currentVideo)
        this.dialogVisible.preview = false
      }
    },
    

    // Delete video
    deleteVideo(video) {
      ElMessageBox.confirm(
        `Are you sure you want to delete the video "${video.title}"?`,
        'Delete Confirmation',
        {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            const { code, msg } = await apiDeleteVideo({videoId:video.id})
            if(code == 0){
              ElMessage.success('Delete successful')
              this.refreshLibrary()
            }else{
              ElMessage.error(msg)
            }
          } catch (error) {
            console.error('Failed to delete video', error)
            ElMessage.error('Failed to delete video')
          }
        })
        .catch(() => {
          ElMessage.info('Delete canceled')
        })
    },

    // Batch delete
    batchDelete() {
      if (this.selectedVideos.length === 0) {
        ElMessage.warning('Please select at least one video')
        return
      }
      
      ElMessageBox.confirm(
        `Are you sure you want to delete the selected ${this.selectedVideos.length} videos?`,
        'Batch Delete Confirmation',
        {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            this.selectedVideos.forEach(async (video) => {
              const { code, msg } = await apiDeleteVideo({videoId:video.id})
              if(code == 0){
                ElMessage.success(`Delete successful:${video.title}`)
                this.refreshLibrary()
              }else{
                ElMessage.error(msg)
              }
            }) 
          } catch (error) {
            console.error('Failed to delete video', error)
            ElMessage.error('Failed to delete video')
          }
        })
        .catch(() => {
          ElMessage.info('Delete canceled')
        })
    },
    
    // Use video
    useVideo(video) {
      // Here you can trigger an event to notify the parent component or other components to use this video
      ElMessage.success(`Video selected: ${video.title}`)
    },
    
    // Use currently previewed video
    useCurrentVideo() {
      if (this.currentVideo) {
        this.useVideo(this.currentVideo)
        this.closePreviewDialog()
      }
    },
    
    // Format duration
    formatDuration(seconds) {
      if (!seconds) return '00:00'
      
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)
      
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    },
    
    // Format date
    formatDate(dateString) {
      return dateString
    },
    
    // Handle video check before upload
    beforeVideoUpload(file) {
      // Check file type and size
      const isVideo = file.type === 'video/mp4' || file.type === 'video/webm'
      const isLt100M = file.size / 1024 / 1024 < 10

      if (!isVideo) {
        ElMessage.error('Uploaded videos can only be in MP4 or WebM format!')
        return false
      }
      
      if (!isLt100M) {
        ElMessage.error('Uploaded video size cannot exceed 10MB!')
        return false
      }
      
      return true
    },
    
    // Handle video file change
    handleUploadChange(file, fileList) {
      this.uploadFiles = fileList
      
      // Auto-fill video name (if not filled)
      if (!this.uploadForm.title && file.name) {
        this.uploadForm.title = file.name
      }
    },
    
    // Remove video file
    handleUploadRemove() {
      this.uploadFiles = []
    },
    
    // Format upload progress
    progressFormat(percentage) {
      return percentage === 100 ? 'Processing...' : `${percentage}%`
    },

    // Submit upload
    async submitUpload() {
      if (this.uploadFiles.length === 0) {
        ElMessage.warning('Please select a video to upload')
        return
      }
      
      if (!this.uploadForm.title) {
        ElMessage.warning('Please enter video name')
        return
      }

      this.uploading = true
      this.uploadProgress = 0
      
      try {
        const formData = new FormData()
        formData.append('file', this.uploadFiles[0].raw)
        formData.append('title', this.uploadForm.title)
        
        if (this.uploadForm.description) {
          formData.append('description', this.uploadForm.description)
        }
        
        // Upload video
        const { code, msg } = await uploadVideo(formData)
        this.uploadProgress = 100
        
        if(code == 0){
          ElMessage.success('Video upload successful')
          this.refreshLibrary() // Re-fetch video list
          
          // Clear form and file list
          this.uploadFiles = []
          this.uploadForm = {
            title: '',
            description: ''
          }
          this.dialogVisible.upload = false
        }else{
          ElMessage.error(msg)
        }
      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.uploading = false
        this.uploadProgress = 0
      }
    }
  },
  mounted() {
    this.fetchVideos()
    
    // Receive refresh method provided by parent component
    if (this.$parent && this.$parent.refreshVideoLibrary) {
      this.$parent.refreshVideoLibrary = this.refreshLibrary
    }
  },
  beforeUnmount() {
    if (this.$refs.videoPlayer) {
      this.$refs.videoPlayer.pause()
    }
  }
}
</script>

<style lang="scss" scoped>
.video-library {
  width: 100%;
}

.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  min-height: 200px;
}

.loading-icon {
  font-size: 32px;
  animation: rotating 2s linear infinite;
}

.video-item {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  position: relative;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    
    .video-actions {
      opacity: 1;
    }
    
    .video-play-icon {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.2);
    }
  }
  
  &.selected {
    border: 2px solid #409eff;
    
    .video-checkbox {
      opacity: 1;
    }
  }
}

.video-wrapper {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: pointer;
  
  .el-image {
    width: 100%;
    height: 100%;
    display: block;
  }
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.7;
  transition: all 0.3s;
  
  .el-icon {
    font-size: 20px;
  }
}

.video-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 2px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  
  .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }
}

.video-info {
  padding: 10px;
}

.video-title {
  font-weight: bold;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-dimension {
  font-size: 12px;
  color: #606266;
  margin-bottom: 3px;
}

.video-date {
  font-size: 12px;
  color: #606266;
  margin-bottom: 3px;
}

.video-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: space-around;
  opacity: 0;
  transition: opacity 0.3s;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.preview-container {
  display: flex;
  flex-direction: column;
  
  .preview-video {
    text-align: center;
    margin-bottom: 20px;
    
    .video-player {
      width: 100%;
      max-height: 400px;
      background-color: #000;
    }
  }
  
  .preview-info {
    border-top: 1px solid #eee;
    padding-top: 15px;
    
    p {
      margin: 5px 0;
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.video-uploader {
  width: 100%;
  padding-left: 80px;
}

.video-uploader .el-upload__tip {
  color: #909399;
  margin-top: 5px;
}

.thumbnail-section {
  margin-top: 20px;
  border-top: 1px dashed #e0e0e0;
  padding-top: 15px;
}

.thumbnail-title {
  margin-bottom: 10px;
  color: #606266;
  font-weight: 500;
}

.thumbnail-uploader {
  width: 100%;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
}

.upload-form {
  margin-top: 20px;
}

.upload-progress {
  margin: 20px 0;
}

.upload-tip {
  color: #909399;
  margin-top: 5px;
}
</style> 