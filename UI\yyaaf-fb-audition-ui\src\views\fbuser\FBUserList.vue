<template>
  <div class="fb-user-container">
    <!-- Page title and action bar -->
    <div class="page-header">
      <div class="header-left">
        <h2>FB User List</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshList" v-if="isGlobalAuthorized">
          <el-icon><Refresh /></el-icon>
          Refresh
        </el-button>
      </div>
    </div>

    <!-- Unauthorized notice -->
    <div v-if="!isGlobalAuthorized" class="auth-required-notice">
      <el-empty 
        description="Please authorize the FB platform first to get user data"
        :image-size="120"
      >
        <template #image>
          <el-icon size="120" color="#409eff">
            <Lock />
          </el-icon>
        </template>
        <el-button 
          type="primary" 
          size="large"
          @click="handleGlobalAuthorize"
          :loading="globalAuthLoading"
        >
          <el-icon><Key /></el-icon>
          Authorize Now
        </el-button>
      </el-empty>
    </div>

    <!-- Content displayed after authorization -->
    <div v-if="isGlobalAuthorized">
      <!-- Search bar -->
      <div class="search-bar">
        <el-form :model="searchForm" inline>
          <el-form-item label="Username">
            <el-input
              v-model="searchForm.name"
              placeholder="Enter username"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">Search</el-button>
            <el-button @click="resetSearch">Reset</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- User list table -->
      <div class="table-container">
        <TablePagination
          :table-data="userList"
          :loading="loading"
          :total="pagination.total"
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          @selection-change="handleSelectionChange"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="id" label="User ID" min-width="200" />
          
          <el-table-column prop="name" label="User Info" min-width="200">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar 
                  :size="40" 
                  :src="row.avatar || generateAvatar(row.name)"
                  class="user-avatar"
                >
                  {{ row.name ? row.name.charAt(0).toUpperCase() : 'U' }}
                </el-avatar>
                <div class="user-details">
                  <div class="user-name">{{ row.name }}</div>
                  <!-- <div class="user-email">{{ row.email }}</div> -->
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="Authorization Status" align="center" min-width="150">
            <template v-slot="{ row }">
              <el-tag type="success" size="medium" v-if="row.oAuthState == 1">Authorized</el-tag>
              <el-tag type="danger" size="medium" v-else-if="row.oAuthState == 0">Unauthorized</el-tag>
              <el-tag type="warning" size="medium" v-else-if="row.oAuthState == 9">Authorization Expired</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="expires"
            label="Authorization Expiry Date"
            align="center"
            min-width="150"
          ></el-table-column>
          
          <!-- <el-table-column prop="createOn" label="Creation Date" width="180">
            <template #default="{ row }">
              {{ row.createOn }}
            </template>
          </el-table-column> -->

          <el-table-column
            prop="lastUpdateTime"
            label="Last Update Time"
            align="center"
            min-width="150"
          ></el-table-column>
          
          <el-table-column label="Actions" min-width="300" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewAdAccounts(row)"
              >
                Ad Accounts
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="viewPages(row)"
              >
                Pages
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleAuthorize(row)"
                :loading="authorizingUsers.includes(row.id)"
              >
                Reauthorize
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                Delete
              </el-button>
            </template>
          </el-table-column>
        </TablePagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getFBUserList, deleteFBUser, getFBOathUrl } from '@/api/fbuser'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Key, Lock } from '@element-plus/icons-vue'
import TablePagination from '@/components/table/TablePagination.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'FBUserList',
  components: {
    Refresh,
    Key,
    Lock,
    TablePagination
  },
  computed: {
    ...mapGetters(['userOAthState']),
    // Use userOAthState from store as global authorization status
    isGlobalAuthorized() {
      return this.userOAthState
    }
  },
  data() {
    return {
      loading: false,
      userList: [],
      selectedUsers: [],
      searchForm: {
        username: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      authorizingUsers: [],
      globalAuthLoading: false // Global authorization loading status
    }
  },
  mounted() {
    this.checkGlobalAuthStatus()
  },
  methods: {
    // Check global authorization status
    async checkGlobalAuthStatus() {
      try {
        // Get authorization status from store
        await this.$store.dispatch('getUserOAthState')
        
        if (this.isGlobalAuthorized) {
          this.fetchUserList()
        }
      } catch (error) {
        console.error('Failed to get authorization status:', error)
        // If failed, check backup status from localStorage
        const localAuthStatus = localStorage.getItem('fb_global_auth_status')
        if (localAuthStatus === 'true') {
          this.fetchUserList()
        }
      }
    },

    // Handle global authorization
    async handleGlobalAuthorize() {
      try {
        this.globalAuthLoading = true
        
        // Simulate global authorization process
        const response = await getFBOathUrl({ id: 'global' })
        
        if (response.code === 0) {
          // Successfully got authorization URL, open new window for authorization
          const authUrl = response.data
          const authWindow = window.open(
            authUrl,
            'facebook_global_auth',
            'width=600,height=700,scrollbars=yes,resizable=yes'
          )
          
          // Listen for authorization window close
          const checkClosed = setInterval(() => {
            if (authWindow.closed) {
              clearInterval(checkClosed)
              // After authorization window closes, update authorization status in store
              setTimeout(async () => {
                try {
                  await this.$store.dispatch('getUserOAthState')
                  if (this.isGlobalAuthorized) {
                    ElMessage.success('FB platform authorization successful! Fetching user data...')
                    this.fetchUserList()
                  } else {
                    ElMessage.warning('Authorization may not be complete, please try again')
                  }
                } catch (error) {
                  console.error('Failed to update authorization status:', error)
                  ElMessage.error('Failed to get authorization status')
                }
              }, 1000)
            }
          }, 1000)
          
        } else {
          ElMessage.error(response.message || 'Failed to get authorization URL')
        }
      } catch (error) {
        console.error('Global authorization failed:', error)
        ElMessage.error('Global authorization failed')
      } finally {
        this.globalAuthLoading = false
      }
    },

    // Fetch user list
    async fetchUserList() {
      if (!this.isGlobalAuthorized) {
        return
      }
      
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          limit: this.pagination.size,
          ...this.searchForm
        }
        
        const response = await getFBUserList(params)
        
        if (response.code === 0) {
          this.userList = response.data || []
          this.pagination.total = response.total || 0
          await this.$store.dispatch('loadFBUserList')
        } else {
          ElMessage.error(response.message || 'Failed to fetch user list')
        }
      } catch (error) {
        console.error('Failed to fetch user list:', error)
        ElMessage.error('Failed to fetch user list')
      } finally {
        this.loading = false
      }
    },

    // Search
    handleSearch() {
      this.pagination.page = 1
      this.fetchUserList()
    },

    // Reset search
    resetSearch() {
      this.searchForm = {
        name: ''
      }
      this.pagination.page = 1
      this.fetchUserList()
    },

    // Refresh list
    refreshList() {
      this.fetchUserList()
    },

    // Selection change
    handleSelectionChange(selection) {
      this.selectedUsers = selection
    },

    // Page size change
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchUserList()
    },

    // Current page change
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchUserList()
    },

    // View ad accounts
    viewAdAccounts(user) {
      // Navigate to FB ad account list page, pass user ID
      this.$router.push({
        name: 'FBAdAccountList',
        query: {
          userId: user.id,
          userName: user.name
        }
      })
    },

    // View pages
    viewPages(user) {
      // Navigate to FB page list page, pass user ID
      this.$router.push({
        name: 'FBPageList',
        query: {
          userId: user.id,
          userName: user.name
        }
      })
    },

    // Delete user
    async handleDelete(user) {
      try {
        await ElMessageBox.confirm(
          `Are you sure you want to delete user "${user.name}"? This action cannot be undone.`,
          'Confirm Deletion',
          {
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            type: 'warning'
          }
        )

        this.loading = true
        const response = await deleteFBUser(user.id)
        
        if (response.code === 0) {
          ElMessage.success('Deleted successfully')
          this.fetchUserList()
        } else {
          ElMessage.error(response.message || 'Failed to delete')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to delete user:', error)
          ElMessage.error('Failed to delete')
        }
      } finally {
        this.loading = false
      }
    },

    // Generate avatar URL
    generateAvatar(name) {
      // Use DiceBear API to generate avatar, or use other avatar services
      const seed = encodeURIComponent(name || 'default')
      return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=409eff&textColor=ffffff`
    },

    // Format date
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },

    // Handle authorization
    async handleAuthorize(user) {
      try {
        this.authorizingUsers.push(user.id)
        
        const response = await getFBOathUrl({ id: user.id })
        
        if (response.code === 0) {
          // Successfully got authorization URL, open new window for authorization
          const authUrl = response.data
          const authWindow = window.open(
            authUrl,
            'facebook_auth',
            'width=600,height=700,scrollbars=yes,resizable=yes'
          )
          
          // Listen for authorization window close
          const checkClosed = setInterval(() => {
            if (authWindow.closed) {
              clearInterval(checkClosed)
              // After authorization window closes, refresh user list
              setTimeout(() => {
                this.fetchUserList()
                ElMessage.success('Authorization process completed, please check authorization status')
              }, 1000)
            }
          }, 1000)
          
        } else {
          ElMessage.error(response.message || 'Failed to get authorization URL')
        }
      } catch (error) {
        console.error('Authorization failed:', error)
        ElMessage.error('Authorization failed')
      } finally {
        this.authorizingUsers = this.authorizingUsers.filter(id => id !== user.id)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.fb-user-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
    
    .status-legend {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      
      .legend-text {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.auth-required-notice {
  margin: 40px 0;
  text-align: center;
  
  .el-empty {
    padding: 40px 20px;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  
  .el-form {
    margin: 0;
  }
}

.table-container {
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .user-avatar {
    flex-shrink: 0;
  }
  
  .user-details {
    flex: 1;
    min-width: 0;
    
    .user-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .user-email {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .fb-user-container {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    
    .header-left {
      h2 {
        font-size: 20px;
      }
    }
  }
  
  .search-bar {
    .el-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;
        
        .el-input {
          width: 100%;
        }
      }
    }
  }
  
  .user-info {
    .user-avatar {
      width: 32px;
      height: 32px;
    }
    
    .user-details {
      .user-name {
        font-size: 14px;
      }
      
      .user-email {
        font-size: 11px;
      }
    }
  }
  
  // Mobile operation button styles
  :deep(.el-table__fixed-right) {
    .el-button {
      margin: 2px;
      padding: 5px 8px;
      font-size: 12px;
      
      &.el-button--small {
        padding: 4px 6px;
      }
    }
  }
}
</style> 