import request from '@/utils/request'

/**
 * 获取仪表盘概览数据
 * @param {Object} params - 查询参数
 * @param {string} params.queryDate - 查询日期，格式为YYYY-MM-DD
 * @returns {Promise} 返回包含指定日期的展示量、点击量、转化量和支出等数据
 */
export function getDashboardOverview(params) {
  return request({
    url: '/dashboard/overview',
    method: 'get',
    params
  })
}

/**
 * 获取广告投放趋势数据
 * @param {string} timeRange - 时间范围：'week'、'month'、'year'
 * @param {Object} params - 查询参数
 * @param {string} params.queryDate - 查询日期，格式为YYYY-MM-DD
 * @returns {Promise} 返回指定时间范围的广告投放趋势数据
 */
export function getAdTrends(timeRange = 'month', params = {}) {
  return request({
    url: '/dashboard/trends',
    method: 'get',
    params: { timeRange, ...params }
  })
}

/**
 * 获取广告类型分布数据
 * @param {Object} params - 查询参数
 * @param {string} params.queryDate - 查询日期，格式为YYYY-MM-DD
 * @returns {Promise} 返回不同类型广告的分布占比数据
 */
export function getAdTypeDistribution(params) {
  return request({
    url: '/dashboard/adtypes',
    method: 'get',
    params
  })
}

/**
 * 获取仪表盘综合数据（一次性获取所有仪表盘数据）
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围：'week'、'month'、'year'
 * @param {string} params.queryDate - 查询日期，格式为YYYY-MM-DD
 * @returns {Promise} 返回仪表盘所需的所有数据
 */
export function getDashboardData(params) {
  return request({
    url: '/dashboard/data',
    method: 'get',
    params
  })
} 