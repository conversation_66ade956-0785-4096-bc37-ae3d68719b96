import request from '@/utils/request'

/**
 * 获取广告列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAdList(params) {
  return request({
    url: '/ads',
    method: 'get',
    params
  })
}

// 同步广告
export function getSyncAds(params) {
  return request({
    url: '/ads/sync',
    method: 'get',
    params
  })
}

// 获取广告选项
export function getAdOptions(adsetId) {
  return request({
    url: '/ads/options',
    method: 'get',
    params: { adsetId }
  })
}

/**
 * 获取广告详情
 * @param {string|number} id - 广告ID
 * @returns {Promise}
 */
export function getAdDetail(id) {
  return request({
    url: `/ads/${id}`,
    method: 'get'
  })
}

/**
 * 创建广告
 * @param {Object} data - 广告数据
 * @returns {Promise}
 */
export function createAd(data) {
  return request({
    url: '/ads',
    method: 'post',
    data
  })
}

/**
 * 更新广告
 * @param {string|number} id - 广告ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateAd(id, data) {
  return request({
    url: `/ads/update/${id}`,
    method: 'post',
    data
  })
}

/**
 * 删除广告
 * @param {string|number} id - 广告ID
 * @returns {Promise}
 */
export function deleteAd(data) {
  return request({
    url: `/ads/delete`,
    method: 'post',
    data
  })
}

/**
 * 更新广告状态
 * @param {string|number} id - 广告ID
 * @param {string} status - 广告状态
 * @returns {Promise}
 */
export function updateAdStatus(id, status) {
  return request({
    url: `/ads/status/${id}`,
    method: 'post',
    data: { status }
  })
}

/**
 * 获取广告统计数据
 * @param {string|number} id - 广告ID
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAdStats(id, params) {
  // 如果id是对象，说明是调用分析页面的批量查询
  if (typeof id === 'object') {
    return request({
      url: '/ads/stats',
      method: 'get',
      params: id
    })
  }
  
  // 否则是查询单个广告的统计
  return request({
    url: `/ads/${id}/stats`,
    method: 'get',
    params
  })
}

/**
 * 获取广告预览数据
 * @param {string} id - 广告ID
 * @returns {Promise}
 */
export function getAdPreview(params) {
  return request({
    url: `/ads/preview`,
    method: 'get',
    params
  })
}

/**
 * 复制现有广告
 * @param {string} id - 要复制的广告ID
 * @param {Object} data - 可选的覆盖数据
 * @returns {Promise}
 */
export function duplicateAd(id, data = {}) {
  return request({
    url: `/ads/${id}/duplicate`,
    method: 'post',
    data
  })
}

/**
 * 预览广告
 * @param {Object} data - 广告数据
 * @returns {Promise}
 */
export function previewAd(data) {
  return request({
    url: '/ads/preview',
    method: 'post',
    data
  })
}
