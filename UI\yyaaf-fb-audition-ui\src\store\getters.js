// 统一的getter文件，集中管理所有模块的getter

const getters = {
  // Auth模块相关getters
  isAuthenticated: state => state.auth.isAuthenticated,
  currentUser: state => state.auth.user,
  isLoading: state => state.auth.loading,
  notification: state => state.auth.notification,
  user: state => state.auth.user,
  userOAthState: state => state.auth.userOAthState,

  // FB选项模块相关getters
  fbUserId: state => state.fbOptions.fbUserId,
  fbAdAccountId: state => state.fbOptions.fbAdAccountId,
  fbUserList: state => state.fbOptions.fbUserList,
  fbAdAccountList: state => state.fbOptions.fbAdAccountList,
  
  // 复合getter - 选中的FB用户
  selectedFBUser: state => {
    const fbUserList = state.fbOptions.fbUserList
    const fbUserId = state.fbOptions.fbUserId
    return fbUserList.find(user => user.id === fbUserId) || null
  },
  
  // 复合getter - 选中的FB广告账户
  selectedFBAdAccount: state => {
    const fbAdAccountList = state.fbOptions.fbAdAccountList
    const fbAdAccountId = state.fbOptions.fbAdAccountId
    return fbAdAccountList.find(account => account.id === fbAdAccountId) || null
  },

  // 业务逻辑getter - 是否有可用的FB用户
  hasAvailableFBUsers: state => {
    return state.fbOptions.fbUserList.length > 0
  },

  // 业务逻辑getter - 是否有可用的FB广告账户
  hasAvailableFBAdAccounts: state => {
    return state.fbOptions.fbAdAccountList.length > 0
  },

  // 业务逻辑getter - 获取已授权的FB用户数量
  authorizedFBUsersCount: state => {
    return state.fbOptions.fbUserList.filter(user => user.oAuthState === 1).length
  },

  // 业务逻辑getter - 获取活跃的广告账户数量
  activeFBAdAccountsCount: state => {
    return state.fbOptions.fbAdAccountList.filter(account => account.status === 'ACTIVE').length
  }
}

export default getters