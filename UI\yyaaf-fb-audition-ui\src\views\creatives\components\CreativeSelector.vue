<template>
  <div class="creative-selector">
    <div class="creative-list">
      <el-table
        :data="creativeList"
        style="width: 100%"
        v-loading="loading"
      >
      <el-table-column label="Creative Preview" width="120">
        <template #default="scope">
          <div class="creative-preview">
            <el-image
              v-if="scope.row.type === 'IMAGE' || scope.row.type === 'CAROUSEL'"
              :src="scope.row.thumbnail_url || scope.row.image_url"
              fit="cover"
              class="preview-image"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div 
              v-else-if="scope.row.type === 'VIDEO'" 
              class="video-preview"
              :style="{ backgroundImage: `url(${scope.row.thumbnail_url})` }"
            >
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
        
        <el-table-column prop="name" label="Creative Name" min-width="200" />
        <el-table-column prop="type" label="Type" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'IMAGE' ? 'success' : 'warning'">
              {{ row.type === 'IMAGE' ? 'Image' : 'Video' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="Title" min-width="200" />
        <el-table-column prop="message" label="Message" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.type === 'IMAGE'" class="ellipsis-text">{{ scope.row.object_story_spec.link_data.message }}</span>
          <span v-else-if="scope.row.type === 'VIDEO'" class="ellipsis-text">{{ scope.row.object_story_spec.video_data.message }}</span>
          <span v-else class="ellipsis-text">--</span>
        </template>
      </el-table-column>
        
        <el-table-column label="Action" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              link
              @click="handleSelect(row)"
            >
              Select
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- Cursor Pagination Component -->
      <div class="pagination-container">
        <cursor-pagination
          :has-next="pagination.hasNext"
          :has-previous="pagination.hasPrevious"
          :after="pagination.after"
          :before="pagination.before"
          @next="handleNextPage"
          @previous="handlePreviousPage"
          prev-text="Prev"
          next-text="Next"
        />
      </div>
    </div>
    
    <div class="selector-footer">
      <el-button @click="$emit('cancel')">Cancel</el-button>
    </div>
  </div>
</template>

<script>
import { getCreativeList } from '@/api/creative'
import { Picture, VideoPlay } from '@element-plus/icons-vue'
import CursorPagination from '@/components/pagination/CursorPagination.vue'

export default {
  name: 'CreativeSelector',
  components: {
    Picture,
    VideoPlay,
    CursorPagination
  },
  
  data() {
    return {
      loading: false,
      creativeList: [],
      pagination: {
        after: '',
        before: '',
        hasNext: false,
        hasPrevious: false
      }
    }
  },
  
  mounted() {
    this.fetchCreativeList()
  },
  
  methods: {
    async fetchCreativeList() {
      this.loading = true
      try {
        // Build query parameters
        const params = {
          limit: 10
        }
        
        // Add Facebook API pagination parameters
        if (this.pagination.after) {
          params.after = this.pagination.after
        }
        if (this.pagination.before) {
          params.before = this.pagination.before
        }
        
        const res = await getCreativeList(params)
        
        if(res.code === 0){
          this.creativeList = res.data || []
          
          // Update pagination information
          this.pagination.after = res.paging?.cursors?.after || ''
          this.pagination.before = res.paging?.cursors?.before || ''
          this.pagination.hasNext = !!res.paging?.next
          this.pagination.hasPrevious = !!res.paging?.previous
        }else{
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.error('Failed to get creative list:', error)
        this.$message.error('Failed to get creative list')
      } finally {
        this.loading = false
      }
    },
    
    // Handle previous page
    handlePreviousPage(pagination) {
      this.pagination.after = pagination.after
      this.pagination.before = pagination.before
      this.fetchCreativeList()
    },
    
    // Handle next page
    handleNextPage(pagination) {
      this.pagination.after = pagination.after
      this.pagination.before = pagination.before
      this.fetchCreativeList()
    },
    
    handleSelect(creative) {
      this.$emit('select', creative)
    }
  }
}
</script>

<style lang="scss" scoped>
.creative-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .creative-list {
    flex: 1;
    overflow: auto;
    padding: 20px;
    
    .creative-preview {
      width: 100px;
      height: 60px;
      border-radius: 4px;
      overflow: hidden;
      
      .preview-image,
      .preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  .pagination-container {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .selector-footer {
    padding: 20px;
    border-top: 1px solid #ebeef5;
    text-align: right;
  }

  .play-icon {
    color: white;
    font-size: 24px;
    z-index: 1;
  }

  .video-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    position: relative;
  }
  
  .image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    color: #909399;
    font-size: 20px;
  }
}
</style> 