import CryptoJS from 'crypto-js'
import AES from 'crypto-js/aes'
import JSEncrypt from 'jsencrypt/bin/jsencrypt'

// aes 加密 key 16, iv 16

// const AESKey = 'b83344f7474f3e0c1950e6eb6ad3ab97'
const AESKey = 'b83344f7474f3e0c1950e6eb6ad3ab97'

const aesOptions = {
  mode: CryptoJS.mode.CBC,
  padding: CryptoJS.pad.Pkcs7,
}
// jO7AWrG2aRRK6s500sC0FQ== 123456
export function encryptAES(message, encryptKey) {
  encryptKey = encryptKey || AESKey
  aesOptions.iv = generateAESIV(encryptKey)
  const encrypted = AES.encrypt(message, generateAESKey(encryptKey), aesOptions)
  // console.log('encrypted:', encrypted.toString())
  return encrypted.toString()
}

/**
 * 获取aes加密用的key
 * @param {String} encryptKey 截取字符串的0-16位
 */
function generateAESKey(encryptKey, length) {
  let key = ''
  length = length || 16
  if (encryptKey.length < length) {
    key = encryptKey.padEnd(length, 'x')
  } else {
    key = encryptKey.substring(0, length)
  }
  key = CryptoJS.enc.Utf8.parse(key)
  return key
}

/**
 * 获取aes加密用的iv
 * @param {String} encryptKey 截取字符串后16位
 */
function generateAESIV(encryptKey) {
  let iv = ''
  const ivLen = 16
  if (encryptKey.length < ivLen) {
    iv = encryptKey.padStart(ivLen, 'x')
  }
  iv = encryptKey.substring(encryptKey.length - ivLen)
  return CryptoJS.enc.Utf8.parse(iv)
}

const publicKey =
  'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMfYk/CK1aLSxfIsQ94dXXsgbXVRLlva\n' +
  'CdQl3egrNJ1+s0WJqgyPdFDIFw/TiwW6cVtYKN5Tm12rC1aVUhE7Mc0CAwEAAQ=='

const commonPublicKey =
  'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALkumIqQMhoRbM5opek++pTvyB/VZCtH\n' +
  'JJqXj0olueiarjwMqFiVgyQjwREddT1y5Sijj1PH9GPLs4GLv9y/y50CAwEAAQ=='

const commonPrivateKey =
  'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAuS6YipAyGhFszmil\n' +
  '6T76lO/IH9VkK0ckmpePSiW56JquPAyoWJWDJCPBER11PXLlKKOPU8f0Y8uzgYu/\n' +
  '3L/LnQIDAQABAkBZhV8UzTSLSZUyC4D5SwrUaT5ztTMhgNj/KvmIPMis2xc+1KRS\n' +
  'KlSSx00+qdm4bKDSB0D80MI5r2FDyov7YUe5AiEA5gT9d+U4bBSguBara4sg62UG\n' +
  'hp21XPsAdWVhpHg0+rsCIQDOGSF9asLMhNreIwgy88zVp/IkyBf/MGmsZYj82Y0J\n' +
  'hwIgBOnmYEFNS0HFjSku0EVQlra5xPZpgWr7P4bC5ziKKTECIQDL900QjQbqVxUw\n' +
  'QGVN38A5NrPKuQgesm/ygK345ujQowIgPNvJ7AO+SS+MPHdnVn9pk7Jwcmlo0aQb\n' +
  'z85W+KC0dBE='

export function encryptRSA(str) {
  return encryptRSAWithKey(str, publicKey)
}

export function encryptRSAWithKey(str, key) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(key)
  return encryptor.encrypt(str)
}

// 解密
export function decryptRSAWithKey(str, key) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(key)
  return encryptor.decrypt(str)
}

export function commonEncryptRSA(str) {
  return encryptRSAWithKey(str, commonPublicKey)
}

export function commonDecryptRSA(str) {
  return decryptRSAWithKey(str, commonPrivateKey)
}
