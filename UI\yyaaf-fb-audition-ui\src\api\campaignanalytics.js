import request from '@/utils/request'

export function campaigninsights(params) {
    return request({
      url: '/analytics/campaign/campaigninsights',
      method: 'get',
      params
    })
  }

  export function overview(params) {
    return request({
      url: '/analytics/campaign/overview',
      method: 'get',
      params
    })
  }

  export function trends(params) {
    return request({
      url: '/analytics/campaign/trends',
      method: 'get',
      params
    })
  }
  
  export function objective(params) {
    return request({
      url: '/analytics/campaign/objective',
      method: 'get',
      params
    })
  }
  
  export function status(params) {
    return request({
      url: '/analytics/campaign/status',
      method: 'get',
      params
    })
  }