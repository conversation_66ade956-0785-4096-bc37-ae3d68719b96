<template>
  <div class="app-wrapper">
    <!-- Sidebar Menu -->
    <div class="sidebar-container" :class="{ 'is-collapsed': isCollapse }">
      <div class="logo-container">
        <img src="@/assets/logo/logo.png" alt="Logo" class="logo-image" />
        <span class="logo-text" v-if="!isCollapse">FB Ad Manager</span>
      </div>
      
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          mode="vertical"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <sidebar-item 
            v-for="route in routes" 
            :key="route.path" 
            :item="route" 
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>
    
    <!-- Main Content -->
    <div class="main-container">
      <!-- Top Navigation -->
      <div class="navbar">
        <div class="left-area">
          <div class="hamburger-container" @click="toggleSidebar">
            <el-icon :size="20">
              <component :is="isCollapse ? 'Expand' : 'Fold'" />
            </el-icon>
          </div>
          <Breadcrumb />
        </div>
        
        <div class="right-area">
          <!-- FB选项选择器 -->
          <FBOptionsSelector />
          
          <div class="right-menu">
            <el-dropdown trigger="click">
              <div class="avatar-container">
                <el-avatar :size="32" :src="userAvatar">{{ userName.charAt(0) }}</el-avatar>
                <span class="user-name">{{ userName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <!--<el-dropdown-item>Profile</el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">Logout</el-dropdown-item>-->
                  <el-dropdown-item @click="handleLogout">Logout</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
      
      <!-- Page Content -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import store from '@/store'
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import SidebarItem from './SidebarItem.vue'
import Breadcrumb from './Breadcrumb.vue'
import FBOptionsSelector from './FBOptionsSelector.vue'
import { Expand, Fold, ArrowDown } from '@element-plus/icons-vue'

// Route information
const route = useRoute()
const router = useRouter()

// Sidebar collapse state
const isCollapse = ref(false)
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// Navigation menu handling
const routes = computed(() => {
  return router.options.routes.filter(route => {
    // Filter routes with hidden or meta.hidden set to true
    if (route.hidden) return false
    if (route.meta && route.meta.hidden) return false
    return true
  })
})

// Active menu
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// Page cache
const cachedViews = ref([])

// User information - 安全访问用户信息
const user = computed(() => store.getters.user)
const userName = computed(() => user.value ? user.value.nickName : 'Guest')
const userAvatar = computed(() => user.value && user.value.avatar ? user.value.avatar : '')

// Logout handler
const handleLogout = async () => {
  await store.dispatch('logout')
  router.push('/login')
}

</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.28s;
  overflow: hidden;
  
  &.is-collapsed {
    width: 64px;
  }
  
  .logo-container {
    height: 60px;
    padding: 10px;
    display: flex;
    align-items: center;
    background-color: #263445;
    
    .logo-image {
      width: 32px;
      height: 32px;
      margin-right: 10px;
    }
    
    .logo-text {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f0f2f5;
}

.navbar {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .left-area {
    display: flex;
    align-items: center;
    
    .hamburger-container {
      padding: 0 15px;
      cursor: pointer;
      transition: background 0.3s;
      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }
  }
  
  .right-area {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .right-menu {
    .avatar-container {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 12px;
      
      .user-name {
        margin: 0 5px;
        font-size: 14px;
      }
    }
  }
}

.app-main {
  flex: 1;
  overflow: auto;
  padding: 15px;
}
</style>