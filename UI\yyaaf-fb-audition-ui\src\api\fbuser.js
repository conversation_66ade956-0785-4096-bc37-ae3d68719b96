import request from '@/utils/request'

// 获取FB用户列表
export function getFBUserList(params) {
    return request({
      url: '/fbuser',
      method: 'get',
      params
    })
}

// 获取FB用户列表
export function getFBUserOptionsList() {
    return request({
        url: `/fbuser/list`,
        method: 'get'
    })
}

// 获取FB用户详情
export function getFBUserDetail(id) {
    return request({
        url: `/fbuser/${id}`,
        method: 'get'
    })
}

// 获取FB用户授权URL
export function getFBOathUrl(params) {
    return request({
        url: `/fbuser/oauth`,
        method: 'post',
        data: params
    })
}


// 删除FB用户
export function deleteFBUser(id) {
    return request({
        url: `/fbuser/delete/${id}`,
        method: 'post'
    })
}

