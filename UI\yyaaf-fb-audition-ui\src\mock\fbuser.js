import Mock from 'mockjs'

// 生成FB用户列表数据
const generateFBUsers = (count = 50) => {
  const users = []
  for (let i = 1; i <= count; i++) {
    const name = Mock.Random.name()
    const oAuthState = Mock.Random.pick([0, 1, 9]) // 0:未授权, 1:已授权, 9:授权过期
    const createOn = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    const lastUpdateTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    
    // 根据授权状态生成过期时间
    let expires = null
    if (oAuthState === 1) {
      // 已授权：生成未来的过期时间
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + Mock.Random.integer(30, 365))
      expires = futureDate.toISOString().split('T')[0] + ' ' + Mock.Random.time('HH:mm:ss')
    } else if (oAuthState === 9) {
      // 授权过期：生成过去的过期时间
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - Mock.Random.integer(1, 30))
      expires = pastDate.toISOString().split('T')[0] + ' ' + Mock.Random.time('HH:mm:ss')
    }
    
    users.push({
      id: i,
      name: name,
      email: Mock.Random.email(),
      status: Mock.Random.pick(['active', 'disabled']),
      oAuthState: oAuthState,
      expires: expires,
      lastUpdateTime: lastUpdateTime,
      createOn: createOn,
      created_at: createOn, // 保持兼容性
      updated_at: lastUpdateTime,
      fb_user_id: Mock.Random.string('number', 10, 15),
      access_token: Mock.Random.string('lower', 50, 100),
      avatar: Mock.Random.boolean() ? `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(name)}&backgroundColor=409eff&textColor=ffffff` : null,
      permissions: Mock.Random.pick([
        ['ads_management', 'ads_read'],
        ['ads_management'],
        ['ads_read'],
        ['ads_management', 'ads_read', 'business_management']
      ])
    })
  }
  return users
}

// 模拟用户数据
let fbUsers = generateFBUsers()

// 获取FB用户选项列表（用于导航栏选择器）
Mock.mock(/\/api\/fbuser\/list/, 'get', (options) => {
  console.log('Mock getFBUserOptionsList called')
  
  // 简化的用户数据，仅用于选择器
  const optionUsers = fbUsers.slice(0, 20).map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    avatar: user.avatar,
    oAuthState: user.oAuthState,
    label: `${user.name} (${user.email})`,
    value: user.id
  }))
  
  return {
    code: 0,
    message: 'success',
    data: optionUsers
  }
})

// FB用户列表接口
Mock.mock(/\/api\/fbuser/, 'get', (options) => {
  const url = new URL(options.url, 'http://localhost')
  const page = parseInt(url.searchParams.get('page')) || 1
  const size = parseInt(url.searchParams.get('size')) || 20
  const username = url.searchParams.get('username') || ''

  // 过滤数据
  let filteredUsers = fbUsers
  if (username) {
    filteredUsers = fbUsers.filter(user => 
      user.name.toLowerCase().includes(username.toLowerCase()) ||
      user.email.toLowerCase().includes(username.toLowerCase())
    )
  }

  // 分页
  const start = (page - 1) * size
  const end = start + size
  const list = filteredUsers.slice(start, end)

  return {
    code: 0,
    message: '获取成功',
    data: {
      list: list,
      total: filteredUsers.length,
      page: page,
      size: size,
      pages: Math.ceil(filteredUsers.length / size)
    }
  }
})

// FB用户详情接口
Mock.mock(/\/api\/fbuser\/(\d+)/, 'get', (options) => {
  const id = parseInt(options.url.match(/\/api\/fbuser\/(\d+)/)[1])
  const user = fbUsers.find(u => u.id === id)
  
  if (user) {
    return {
      code: 0,
      message: '获取成功',
      data: user
    }
  } else {
    return {
      code: 404,
      message: '用户不存在'
    }
  }
})

// 获取FB用户授权URL接口
Mock.mock(/\/api\/fbuser\/oauth/, 'post', (options) => {
  const body = JSON.parse(options.body || '{}')
  const userId = body.userId
  
  if (!userId) {
    return {
      code: 400,
      message: '用户ID不能为空'
    }
  }
  
  // 处理全局授权
  if (userId === 'global') {
    const state = Mock.Random.string('lower', 32)
    const clientId = Mock.Random.string('number', 15, 16)
    const redirectUri = encodeURIComponent('http://localhost:8080/auth/global/callback')
    const scope = encodeURIComponent('ads_management,ads_read,business_management,pages_read_engagement')
    
    // 模拟授权成功，设置localStorage状态
    // 注意：在实际应用中，这应该在授权回调中设置
    setTimeout(() => {
      localStorage.setItem('fb_global_auth_status', 'true')
    }, 2000) // 模拟授权延迟
    
    const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&state=${state}&auth_type=rerequest`
    
    return {
      code: 0,
      message: '获取全局授权URL成功',
      data: {
        authUrl: authUrl,
        state: state,
        userId: 'global',
        authType: 'global'
      }
    }
  }
  
  // 处理单个用户授权
  const user = fbUsers.find(u => u.id === parseInt(userId))
  if (!user) {
    return {
      code: 404,
      message: '用户不存在'
    }
  }
  
  // 生成模拟的Facebook授权URL
  const state = Mock.Random.string('lower', 32)
  const clientId = Mock.Random.string('number', 15, 16)
  const redirectUri = encodeURIComponent('http://localhost:8080/auth/callback')
  const scope = encodeURIComponent('ads_management,ads_read,business_management')
  
  const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&state=${state}`
  
  return {
    code: 0,
    message: '获取授权URL成功',
    data: {
      authUrl: authUrl,
      state: state,
      userId: userId
    }
  }
})

// 删除FB用户接口
Mock.mock(/\/api\/fbuser\/delete\/(\d+)/, 'post', (options) => {
  const id = parseInt(options.url.match(/\/api\/fbuser\/delete\/(\d+)/)[1])
  const index = fbUsers.findIndex(u => u.id === id)
  
  if (index !== -1) {
    fbUsers.splice(index, 1)
    return {
      code: 0,
      message: '删除成功'
    }
  } else {
    return {
      code: 404,
      message: '用户不存在'
    }
  }
})

export default {
  fbUsers
} 