const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  // transpileDependencies: [
  //   // 添加需要被 Babel 转译的依赖包名
  //   '@vue/compiler-sfc'
  // ]
  // 其他配置...
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    open: false,
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: 'http://localhost:56761',
        changeOrigin: true,
      }
    }
  },
  
  // 生产环境配置
  productionSourceMap: false,
  
  // 公共路径配置
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  
  // 输出目录配置
  outputDir: 'dist',
  
  // 静态资源目录配置
  assetsDir: 'static'
}) 