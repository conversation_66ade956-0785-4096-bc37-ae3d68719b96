import request from '@/utils/request'

// 获取国家列表
export function getCountryList() {
  return request({
    url: '/options/getFBCountryOptions',
    method: 'get'
  })
}

// 获取FB用户列表
export function getFBUserList() {
  return request({
    url: '/options/getFBUserOptions',
    method: 'get'
  })
}

// 获取FB广告账户列表
export function getFBAdAccountList(fbUserId) {
  return request({
    url: '/options/getFBAdAccountOptions',
    method: 'get',
    params: {
      fbUserId: fbUserId
    }
  })
}

// 获取FB公共主页列表
export function getFBPageOptions(params) {
  return request({
    url: '/options/getFBPageOptions',
    method: 'get',
    params
  })
}