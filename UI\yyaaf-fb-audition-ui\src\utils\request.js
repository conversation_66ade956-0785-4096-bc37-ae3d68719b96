import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import router from '@/router'
import store from '@/store'
import { getToken, removeToken } from '@/utils/auth'

// Create axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API || '/api', // url = base url + request url
  timeout: 30000 // Request timeout
})

// Request interceptor
service.interceptors.request.use(
  config => {
    // Do something before sending request
    const token = getToken()
    if (token) {
      // Attach custom token to each request
      //config.headers['Authorization'] = `Bearer ${token}`
      config.headers['Access-Token'] = token
    }
    
    // Add global FB option parameters
    const fbUserId = store.getters.fbUserId
    const fbAdAccountId = store.getters.fbAdAccountId
    const fbOptions = {
      FBSelectUserId: fbUserId || '',
      FBSelectAdAccountId: fbAdAccountId || ''
    }
    if (config.method === 'get') {
      config.params = Object.assign(config.params || {}, fbOptions)
    }
    if (config.method === 'post') {
      config.data = Object.assign(config.data || {}, fbOptions)
    }
    
    return config
  },
  error => {
    // Do something with request error
    console.error(error)
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    // Do something with response data
    const res = response.data
    
    // If custom code is not 0, it is considered an error
    if (res.code !== 0 && res.code !== 200) {
      /*ElMessage({
        message: res.msg || '请求错误',
        type: 'error',
        duration: 5 * 1000
      })*/
      
      // Token verification failed
      if (res.code === 401 || res.code === 403) {
        // Re-login
        ElMessageBox.confirm('Login status has expired, please log in again', 'System Prompt', {
          confirmButtonText: 'Re-login',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          removeToken()
          router.push('/login')
        })
      }
      
      return Promise.reject(new Error(res.msg || 'Request error'))
    } else {
      return res
    }
  },
  error => {
    // Do something with response error
    let message = error.msg || 'Request error'
    
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = 'Unauthorized, please log in again'
          // Clear token and redirect to login page
          removeToken()
          router.push('/login')
          break
        case 403:
          message = 'Access denied'
          break
        case 404:
          message = 'Request address error'
          break
        case 500:
          //message = 'Internal server error'
          message = error.response.data.message || `Request failed (${error.response.status})`
          break
        default:
          message = error.response.data.message || `Request failed (${error.response.status})`
      }
    } else if (error.request) {
      message = 'Server did not respond'
    }
    
    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    
    return Promise.reject(error)
  }
)

export default service