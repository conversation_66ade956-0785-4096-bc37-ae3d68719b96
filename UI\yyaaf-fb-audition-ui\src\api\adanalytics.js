import request from '@/utils/request'

export function adinsights(params) {
    return request({
      url: '/analytics/ad/adinsights',
      method: 'get',
      params
    })
  }
  
  export function overview(params) {
    return request({
      url: '/analytics/ad/overview',
      method: 'get',
      params
    })
  }

  export function trends(params) {
    return request({
      url: '/analytics/ad/trends',
      method: 'get',
      params
    })
  }

  export function adcompare(params) {
    return request({
      url: '/analytics/ad/adcompare',
      method: 'get',
      params
    })
  }

  export function previouscomparison(params) {
    return request({
      url: '/analytics/ad/previouscomparison',
      method: 'get',
      params
    })
  }
