// 广告系列分析模拟数据

// 生成日期数组，最近30天
const generateDates = (days = 30) => {
  const dates = [];
  const today = new Date();
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    dates.push(date.toISOString().split('T')[0]);
  }
  return dates;
};

// 生成随机趋势数据
const generateTrends = (days = 30, seed = 1000) => {
  const dates = generateDates(days);
  return dates.map((date, index) => {
    // 添加一些随机波动，但保持整体趋势
    const factor = 1 + (Math.random() * 0.3 - 0.1) + (index / days) * 0.5;
    return {
      date,
      impressions: Math.floor(seed * factor * (1 + Math.random() * 0.5)),
      clicks: Math.floor((seed * factor * (1 + Math.random() * 0.5)) / 20),
      conversions: Math.floor((seed * factor * (1 + Math.random() * 0.5)) / 200),
      spend: +(seed * factor * (1 + Math.random() * 0.5) / 100).toFixed(2),
      ctr: +(Math.random() * 0.05 + 0.01).toFixed(4),
      conversionRate: +(Math.random() * 0.03 + 0.005).toFixed(4),
      cpc: +(Math.random() * 2 + 0.5).toFixed(2),
      cpm: +(Math.random() * 30 + 10).toFixed(2),
    };
  });
};

// 生成广告系列列表
const generateCampaigns = (count = 20) => {
  const objectives = ['BRAND_AWARENESS', 'TRAFFIC', 'APP_INSTALLS', 'VIDEO_VIEWS', 'CONVERSIONS'];
  const statuses = ['ACTIVE', 'PAUSED', 'ARCHIVED'];
  
  return Array.from({ length: count }, (_, i) => {
    const impressions = Math.floor(10000 + Math.random() * 90000);
    const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01));
    const conversions = Math.floor(clicks * (Math.random() * 0.1 + 0.05));
    const spend = +(Math.random() * 5000 + 1000).toFixed(2);
    const ctr = +(clicks / impressions).toFixed(4);
    const conversionRate = +(conversions / clicks).toFixed(4);
    const cpc = +(spend / clicks).toFixed(2);
    const cpm = +((spend / impressions) * 1000).toFixed(2);
    
    return {
      id: `campaign_${i + 1}`,
      name: `测试广告系列 ${i + 1}`,
      objective: objectives[Math.floor(Math.random() * objectives.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      impressions,
      clicks,
      ctr,
      conversions,
      conversionRate,
      spend,
      cpc,
      cpm,
      startDate: '2023-01-01',
      endDate: '2023-12-31',
    };
  });
};

// 生成目标分布数据
const generateObjectiveDistribution = () => {
  return [
    { name: 'BRAND_AWARENESS', value: Math.floor(Math.random() * 30 + 10) },
    { name: 'TRAFFIC', value: Math.floor(Math.random() * 30 + 20) },
    { name: 'APP_INSTALLS', value: Math.floor(Math.random() * 20 + 5) },
    { name: 'VIDEO_VIEWS', value: Math.floor(Math.random() * 25 + 15) },
    { name: 'CONVERSIONS', value: Math.floor(Math.random() * 40 + 30) },
  ];
};

// 生成状态分布数据
const generateStatusDistribution = () => {
  return {
    ACTIVE: Math.floor(Math.random() * 50 + 30),
    PAUSED: Math.floor(Math.random() * 30 + 10),
    ARCHIVED: Math.floor(Math.random() * 20 + 5),
  };
};

// 生成基准对比数据
const generateBenchmarks = () => {
  return [
    { name: '行业平均', roi: +(Math.random() * 2 + 1).toFixed(2), ctr: +(Math.random() * 0.03 + 0.01).toFixed(4), conversionRate: +(Math.random() * 0.02 + 0.01).toFixed(4), cpm: +(Math.random() * 20 + 10).toFixed(2) },
    { name: '你的广告系列', roi: +(Math.random() * 3 + 2).toFixed(2), ctr: +(Math.random() * 0.04 + 0.02).toFixed(4), conversionRate: +(Math.random() * 0.03 + 0.015).toFixed(4), cpm: +(Math.random() * 15 + 8).toFixed(2) },
    { name: '超级旗舰机', roi: +(Math.random() * 4 + 3).toFixed(2), ctr: +(Math.random() * 0.05 + 0.03).toFixed(4), conversionRate: +(Math.random() * 0.04 + 0.02).toFixed(4), cpm: +(Math.random() * 25 + 12).toFixed(2) },
    { name: '游戏推广', roi: +(Math.random() * 3.5 + 1.5).toFixed(2), ctr: +(Math.random() * 0.035 + 0.015).toFixed(4), conversionRate: +(Math.random() * 0.025 + 0.01).toFixed(4), cpm: +(Math.random() * 22 + 15).toFixed(2) },
    { name: '服装品牌', roi: +(Math.random() * 2.5 + 2).toFixed(2), ctr: +(Math.random() * 0.04 + 0.025).toFixed(4), conversionRate: +(Math.random() * 0.035 + 0.02).toFixed(4), cpm: +(Math.random() * 18 + 12).toFixed(2) },
    { name: '教育培训', roi: +(Math.random() * 5 + 3).toFixed(2), ctr: +(Math.random() * 0.06 + 0.04).toFixed(4), conversionRate: +(Math.random() * 0.05 + 0.03).toFixed(4), cpm: +(Math.random() * 30 + 20).toFixed(2) },
    { name: '旅游推广', roi: +(Math.random() * 3 + 1).toFixed(2), ctr: +(Math.random() * 0.045 + 0.02).toFixed(4), conversionRate: +(Math.random() * 0.025 + 0.015).toFixed(4), cpm: +(Math.random() * 25 + 15).toFixed(2) },
    { name: '美妆产品', roi: +(Math.random() * 4 + 2).toFixed(2), ctr: +(Math.random() * 0.055 + 0.03).toFixed(4), conversionRate: +(Math.random() * 0.04 + 0.025).toFixed(4), cpm: +(Math.random() * 28 + 18).toFixed(2) },
    { name: '房地产', roi: +(Math.random() * 2 + 0.5).toFixed(2), ctr: +(Math.random() * 0.02 + 0.01).toFixed(4), conversionRate: +(Math.random() * 0.01 + 0.005).toFixed(4), cpm: +(Math.random() * 40 + 25).toFixed(2) },
    { name: '金融服务', roi: +(Math.random() * 6 + 4).toFixed(2), ctr: +(Math.random() * 0.03 + 0.02).toFixed(4), conversionRate: +(Math.random() * 0.06 + 0.04).toFixed(4), cpm: +(Math.random() * 50 + 30).toFixed(2) },
  ];
};

// 生成概览数据
const generateOverview = () => {
  const trends = generateTrends();
  const totalImpressions = trends.reduce((sum, item) => sum + item.impressions, 0);
  const totalClicks = trends.reduce((sum, item) => sum + item.clicks, 0);
  const totalConversions = trends.reduce((sum, item) => sum + item.conversions, 0);
  const totalSpend = trends.reduce((sum, item) => sum + item.spend, 0);
  
  return {
    totalImpressions,
    totalClicks,
    totalConversions,
    totalSpend,
    impressionsTrend: +(Math.random() * 30 - 10).toFixed(1),
    clicksTrend: +(Math.random() * 40 - 15).toFixed(1),
    conversionsTrend: +(Math.random() * 50 - 20).toFixed(1),
    spendTrend: +(Math.random() * 25 - 5).toFixed(1),
  };
};

// 获取广告系列分析数据
export function getCampaignAnalytics(params) {
  const { page = 1, pageSize = 10 } = params;
  const allCampaigns = generateCampaigns(50);
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  
  return {
    items: allCampaigns.slice(start, end),
    total: allCampaigns.length,
    page,
    pageSize,
    trends: generateTrends(),
    objectiveDistribution: generateObjectiveDistribution(),
    statusDistribution: generateStatusDistribution(),
    benchmarks: generateBenchmarks(),
    overview: generateOverview(),
  };
}

// 获取广告系列详情分析数据
export function getCampaignDetailAnalytics(id, params) {
  const campaign = generateCampaigns(1)[0];
  campaign.id = id;
  
  return {
    campaign,
    trends: generateTrends(),
    demographics: [
      { age: '18-24', percentage: Math.floor(Math.random() * 20 + 5) },
      { age: '25-34', percentage: Math.floor(Math.random() * 30 + 20) },
      { age: '35-44', percentage: Math.floor(Math.random() * 25 + 15) },
      { age: '45-54', percentage: Math.floor(Math.random() * 15 + 5) },
      { age: '55+', percentage: Math.floor(Math.random() * 10 + 2) },
    ],
    genders: [
      { gender: 'male', percentage: Math.floor(Math.random() * 60 + 30) },
      { gender: 'female', percentage: Math.floor(Math.random() * 60 + 30) },
      { gender: 'unknown', percentage: Math.floor(Math.random() * 10) },
    ],
    locations: [
      { country: '中国', percentage: Math.floor(Math.random() * 70 + 20) },
      { country: '美国', percentage: Math.floor(Math.random() * 20 + 5) },
      { country: '日本', percentage: Math.floor(Math.random() * 15 + 3) },
      { country: '韩国', percentage: Math.floor(Math.random() * 12 + 2) },
      { country: '其他', percentage: Math.floor(Math.random() * 10 + 1) },
    ],
    devices: [
      { device: 'mobile', percentage: Math.floor(Math.random() * 70 + 20) },
      { device: 'desktop', percentage: Math.floor(Math.random() * 40 + 10) },
      { device: 'tablet', percentage: Math.floor(Math.random() * 20 + 5) },
    ],
    placements: [
      { placement: 'feed', percentage: Math.floor(Math.random() * 60 + 20) },
      { placement: 'story', percentage: Math.floor(Math.random() * 30 + 10) },
      { placement: 'search', percentage: Math.floor(Math.random() * 20 + 5) },
      { placement: 'video', percentage: Math.floor(Math.random() * 25 + 8) },
      { placement: 'other', percentage: Math.floor(Math.random() * 15 + 2) },
    ],
  };
} 