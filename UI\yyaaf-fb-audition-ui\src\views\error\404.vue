<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <h1>404</h1>
      <h2>Sorry, the page you visited does not exist</h2>
      <div class="actions">
        <el-button type="primary" @click="goBack">Go Back</el-button>
        <el-button @click="goHome">Go Home</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/dashboard')
}
</script>

<style lang="scss" scoped>
.not-found-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  
  .not-found-content {
    text-align: center;
    
    h1 {
      font-size: 120px;
      margin: 0;
      color: #409eff;
      text-shadow: 5px 5px 0 rgba(0, 0, 0, 0.1);
    }
    
    h2 {
      font-size: 24px;
      color: #606266;
      margin: 20px 0 40px;
    }
    
    .actions {
      display: flex;
      justify-content: center;
      gap: 20px;
    }
  }
}
</style>