<template>
  <div class="image-library">
    <!-- Search Filter Area -->
    <div class="filter-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="Material Name">
          <el-input
            v-model="searchForm.name"
            placeholder="Please enter material name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            Search
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            Reset
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Batch Operations Area -->
    <div class="batch-actions">
      <div class="left-actions">
        <el-button type="primary" @click="dialogVisible.upload = true">
          <el-icon><Upload /></el-icon>
          Upload Image
        </el-button>
      </div>
      <div class="right-actions">
        <el-button size="small" type="danger" @click="batchDelete">
          <el-icon><Delete /></el-icon>
          Batch Delete ({{ selectedImages.length }})
        </el-button>
      </div>
    </div>

    <!-- Image List -->
    <div class="image-grid">
      <el-empty v-if="loading" description="Loading...">
        <template #image>
          <el-icon class="loading-icon"><Loading /></el-icon>
        </template>
      </el-empty>
      
      <el-empty v-else-if="images.length === 0" description="No image materials yet" />
      
      <template v-else>
        <div
          v-for="image in images"
          :key="image.id"
          class="image-item"
          :class="{ selected: selectedImages.includes(image) }"
          @click="toggleSelectImage(image.id)"
        >
          <div class="image-wrapper">
            <el-image
              :src="image.url"
              fit="cover"
              :initial-index="0"
              @click.stop="previewImage(image)"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><PictureFilled /></el-icon>
                  <span>Load failed</span>
                </div>
              </template>
            </el-image>
            <div class="image-checkbox" @click.stop="toggleSelectImage(image.id)">
              <el-checkbox v-model="image.selected" :label="image.name" />
            </div>
          </div>
          <div class="image-info">
            <div class="image-name" :title="image.name">{{ image.name }}</div>
            <div class="image-dimension">{{ image.width }} x {{ image.height }}</div>
            <div class="image-date">{{ formatDate(image.created_time) }}</div>
          </div>
          <div class="image-actions">
            <el-button
              type="primary"
              size="small"
              text
              @click.stop="toggleSelectImage(image.id)"
            >
              Select
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click.stop="deleteImage(image)"
            >
              Delete
            </el-button>
          </div>
        </div>
      </template>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
      <el-button 
        :disabled="!pagination.hasPrevious" 
        @click="loadPreviousPage"
        icon="ArrowLeft"
      >
        Previous
      </el-button>
      <el-button 
        :disabled="!pagination.hasNext" 
        @click="loadNextPage"
        icon="ArrowRight"
      >
        Next
      </el-button>
    </div>

    <!-- Image Preview Dialog -->
    <el-dialog
      v-model="dialogVisible.preview"
      title="Image Preview"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentImage" class="preview-container">
        <div class="preview-image">
          <el-image :src="currentImage.url" :preview-src-list="[currentImage.url]" fit="contain" />
        </div>
        <div class="preview-info">
          <p><strong>Name:</strong>{{ currentImage.name }}</p>
          <p><strong>Dimensions:</strong>{{ currentImage.width }} x {{ currentImage.height }}</p>
          <p><strong>Creation Time:</strong>{{ formatDate(currentImage.created_time) }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="danger" @click="deleteCurrentImage()">Delete Image</el-button>
          <el-button @click="dialogVisible.preview = false">Close</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Add Upload Image Dialog -->
    <el-dialog
      v-model="dialogVisible.upload"
      title="Upload Image"
      width="600px"
      destroy-on-close
    >
      <el-upload
        class="image-uploader"
        :action="''"
        :auto-upload="false"
        multiple
        :limit="10"
        :on-exceed="handleUploadExceed"
        :on-change="handleUploadChange"
        :on-remove="handleUploadRemove"
        :file-list="uploadFiles"
        list-type="picture-card"
      >
        <el-icon class="upload-icon"><Plus /></el-icon>
      </el-upload>
      <div class="upload-tip">
        Supports JPG, PNG format, single file does not exceed 5MB, up to 10 images can be uploaded
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible.upload = false">Cancel</el-button>
          <el-button type="primary" :loading="uploading" @click="submitUpload">
            {{ uploading ? 'Uploading...' : 'Start Upload' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Search, Refresh, Delete, Loading, PictureFilled, Upload, Plus, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getImageList, 
  deleteImage as apiDeleteImage, 
  uploadImage,
} from '@/api/media'

export default {
  name: 'ImageLibrary',
  components: {
    Search,
    Refresh,
    Delete,
    //PriceTag,
    Loading,
    PictureFilled,
    Upload,
    Plus,
    //ArrowLeft,
    //ArrowRight
  },
  data() {
    return {
      // Search form
      searchForm: {
        name: '',
      },
      
      // Table loading status
      loading: false,
      
      // Image list
      images: [],
      
      // Pagination information
      pagination: {
        after: '',
        before: '',
        hasNext: false,
        hasPrevious: false
      },
      
      // Dialog visible status
      dialogVisible: {
        preview: false,
        upload: false
      },
      
      // Current operating image
      currentImage: null,
  
      // Upload related
      uploadFiles: [],
      uploadForm: {
      },
      uploading: false
    }
  },
  computed: {
    // Selected image ID
    selectedImages() {
      return this.images
        .filter(item => item.selected)
        .map(item => item)
    }
  },
  methods: {
    // Define refresh function
    refreshLibrary() {
      this.pagination.after = ''
      this.pagination.before = ''
      this.fetchImages()
    },
    
    // Get image list
    async fetchImages(action) {
      this.loading = true
      
      try {
        // Build query parameters
        const params = {
          limit: 24
        }

        // Add Facebook API pagination parameters
        if (this.pagination.after) {
          params.after = this.pagination.after
        }
        
        if (this.pagination.before) {
          params.before = this.pagination.before
        }

        // Add search criteria
        if (this.searchForm.name) {
          params.name = this.searchForm.name
        }
        console.log('API Request:', params)
        // Call API to get image list
        const res = await getImageList(params)
        console.log('API Response:', res)
        
        // Process image data
        this.images = res.data.map(item => ({
          ...item,
          selected: false
        }))
        
        // Update pagination information
        this.pagination.after = res.paging?.cursors?.after || ''
        this.pagination.before = res.paging?.cursors?.before || ''
        this.pagination.hasNext = !!res.paging?.next
        this.pagination.hasPrevious = !!res.paging?.previous

        console.log('pagination:', this.pagination)
      } catch (error) {
        console.error('Failed to get image list', error)
        ElMessage.error('Failed to get image list')
      } finally {
        this.loading = false
      }
    },
    
    // Search
    handleSearch() {
      this.pagination.after = ''
      this.pagination.before = ''
      this.fetchImages()
    },
    
    // Reset search criteria
    resetSearch() {
      Object.keys(this.searchForm).forEach(key => {
        if (Array.isArray(this.searchForm[key])) {
          this.searchForm[key] = []
        } else {
          this.searchForm[key] = ''
        }
      })
      this.handleSearch()
    },
    
    // Load previous page
    loadPreviousPage() {
      if (this.pagination.hasPrevious) {
        this.pagination.after = '' // Clear after parameter
        this.fetchImages()
      }
    },
    
    // Load next page
    loadNextPage() {
      if (this.pagination.hasNext) {
        this.pagination.before = '' // Clear before parameter
        this.fetchImages()
      }
    },
    
    // Toggle selected image
    toggleSelectImage(id) {
      const image = this.images.find(item => item.id === id)
      if (image) {
        image.selected = !image.selected
        // Force update view
        this.images = [...this.images]
      }
    },
    
    // Preview image
    previewImage(image) {
      this.currentImage = image
      this.dialogVisible.preview = true
    },
    
    // Delete image
    deleteImage(image) {
      ElMessageBox.confirm(
        `Are you sure you want to delete the image "${image.name}"?`,
        'Delete Confirmation',
        {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            const { code,msg } = await apiDeleteImage({hash:image.hash})
            if(code == 0){
              ElMessage.success('Delete successful')
              this.refreshLibrary()
            }else{
              ElMessage.error(msg)
            }
          } catch (error) {
            console.error('Failed to delete image', error)
            ElMessage.error('Failed to delete image')
          }
        })
        .catch(() => {
          ElMessage.info('Delete canceled')
        })
    },
    
    // Use image
    useImage(image) {
      // Here you can trigger an event to notify the parent component or other components to use this image
      ElMessage.success(`Image selected: ${image.name}`)
    },
    
    // Delete currently previewed image
    deleteCurrentImage() {
      if (this.currentImage) {
        this.deleteImage(this.currentImage)
        this.dialogVisible.preview = false
      }
    },
    
    // Batch delete
    batchDelete() {
      if (this.selectedImages.length === 0) {
        ElMessage.warning('Please select at least one image')
        return
      }
      
      ElMessageBox.confirm(
        `Are you sure you want to delete the selected ${this.selectedImages.length} images?`,
        'Batch Delete Confirmation',
        {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            this.selectedImages.forEach(async (image) => {
              const { code, msg } = await apiDeleteImage({hash:image.hash})
              if(code == 0){
                ElMessage.success(`Delete successful:${image.name}`)
              }else{
                ElMessage.error(msg)
              }
            }) 
            this.refreshLibrary()
          } catch (error) {
            console.error('Failed to batch delete images', error)
            ElMessage.error('Failed to batch delete images')
          }
        })
        .catch(() => {
          ElMessage.info('Delete canceled')
        })
    },
    
    // Format date
    formatDate(dateString) {
      return dateString
    },
    
    // Handle exceeding upload file limit
    handleUploadExceed() {
      ElMessage.warning('You can upload up to 10 images')
    },
    
    // Handle upload file change
    handleUploadChange(file, fileList) {
      // Check file type and size
      const isImage = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
      const isLt5M = file.raw.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('Uploaded images can only be in JPG or PNG format!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      if (!isLt5M) {
        ElMessage.error('Uploaded image size cannot exceed 5MB!')
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        return
      }
      
      this.uploadFiles = fileList
    },
    
    // Handle remove upload file
    handleUploadRemove(file, fileList) {
      this.uploadFiles = fileList
    },
    
    // Submit upload
    async submitUpload() {
      if (this.uploadFiles.length === 0) {
        ElMessage.warning('Please select images to upload')
        return
      }

      this.uploading = true
      
      try {
        const formData = new FormData()
        this.uploadFiles.forEach(file => {
          formData.append('files[]', file.raw)
        })
        const { code, msg } = await uploadImage(formData)
        if(code == 0){
          ElMessage.success('Upload successful')
          this.refreshLibrary() // Re-fetch image list
        }else{
          ElMessage.error(msg)
        }

        // Clear form and file list
        this.uploadFiles = []
        //this.uploadForm.tags = []
        this.dialogVisible.upload = false

      } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error('Upload failed, please try again')
      } finally {
        this.uploading = false
      }
    }
  },
  mounted() {
    this.fetchImages()
    
    // Receive refresh method provided by parent component
    if (this.$parent && this.$parent.refreshImageLibrary) {
      this.$parent.refreshImageLibrary = this.refreshLibrary
    }
  }
}
</script>

<style lang="scss" scoped>
.image-library {
  width: 100%;
}

.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  min-height: 200px;
}

.loading-icon {
  font-size: 32px;
  animation: rotating 2s linear infinite;
}

.image-item {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  position: relative;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    
    .image-actions {
      opacity: 1;
    }
  }
  
  &.selected {
    border: 2px solid #409eff;
    
    .image-checkbox {
      opacity: 1;
    }
  }
}

.image-wrapper {
  position: relative;
  height: 160px;
  overflow: hidden;
  
  .el-image {
    width: 100%;
    height: 100%;
    display: block;
  }
}

.image-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 2px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  
  .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }
}

.image-info {
  padding: 10px;
}

.image-name {
  font-weight: bold;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-size,
.image-dimension,
.image-date {
  font-size: 12px;
  color: #606266;
  margin-bottom: 3px;
}

/*.image-tags {
  margin: 5px 0;
  
  .tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}*/

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: space-around;
  opacity: 0;
  transition: opacity 0.3s;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.preview-container {
  display: flex;
  flex-direction: column;
  
  .preview-image {
    text-align: center;
    margin-bottom: 20px;
    
    .el-image {
      max-height: 400px;
    }
  }
  
  .preview-info {
    border-top: 1px solid #eee;
    padding-top: 15px;
    
    p {
      margin: 5px 0;
    }
    
    /*.tag {
      margin-right: 5px;
    }*/
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.image-uploader {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-bottom: 15px;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
}

.upload-tip {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 10px;
  text-align: center;
}
</style> 