import { login, logout, getInfo, getUserOAthState } from '@/api/login'
import { setToken, removeToken } from '@/utils/auth'

const auth = {
  state: {
    user: null,
    isAuthenticated: false,
    loading: false,
    notification: null,
    userOAthState: false
  },

  mutations: {
    SET_USER(state, user) {
      state.user = user
      state.isAuthenticated = !!user
    },
    
    SET_LOADING(state, status) {
      state.loading = status
    },
    
    SET_NOTIFICATION(state, notification) {
      state.notification = notification
    },
    
    CLEAR_NOTIFICATION(state) {
      state.notification = null
    },
    
    SET_USER_OATH_STATE(state, userOAthState) {
      state.userOAthState = userOAthState
    }
  },

  actions: {
    login({ commit }, userData) {
      commit('SET_LOADING', true)
      return new Promise((resolve, reject) => {
        login({ 
          username: userData.username.trim(), 
          password: userData.password 
        })
        .then((response) => {
          if (response.code === 0) {
            const { data } = response
            setToken(data.token)
            commit('SET_LOADING', false)
            resolve()
          } else {
            reject(new Error(response.msg))
          }
        })
        .catch((error) => {
          reject(error)
        })
      })
    },

    logout({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        logout()
        .then(() => {
          removeToken() // must remove token first
          commit('SET_USER', null)
          // 清空FB选项状态
          dispatch('clearFBOptions')
          resolve()
        })
        .catch((error) => {
          removeToken() // must remove token first
          commit('SET_USER', null)
          // 清空FB选项状态
          dispatch('clearFBOptions')
          reject(error)
        })
      })
    },

    getInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((response) => {
            const { data } = response
            if (response.code != 0) {
              reject(response.msg || 'Authentication failed. Please log in again')
            }
            const { nickName, avatar, userId } = data
            commit('SET_USER', data)
            resolve(data)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    getUserOAthState({ commit }) {
      return new Promise((resolve, reject) => {
        getUserOAthState()
          .then((response) => {
            commit('SET_USER_OATH_STATE', response.data)
            resolve(response.data)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    showNotification({ commit }, notification) {
      commit('SET_NOTIFICATION', notification)
      if (notification.timeout !== 0) {
        setTimeout(() => {
          commit('CLEAR_NOTIFICATION')
        }, notification.timeout || 3000)
      }
    }
  }
}

export default auth 