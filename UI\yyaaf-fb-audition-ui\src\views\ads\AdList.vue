<template>
  <div class="ad-list-container">
    <!-- Page Title -->
    <div class="page-header">
      <h2 class="page-title">Ad Management</h2>
      <el-button type="primary" @click="handleCreateAd">
        <el-icon><Plus /></el-icon>Create Ad
      </el-button>
    </div>

    <!-- Search and Filter Area -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="Ad Name">
          <el-input
            v-model="queryParams.name"
            placeholder="Please enter ad name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="Ad ID">
          <el-input
            v-model="queryParams.id"
            placeholder="Please enter ad ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="Ad Set">
          <el-select
            v-model="queryParams.adset_id"
            placeholder="Please select ad set"
            clearable
            filterable
          >
            <el-option
              v-for="item in adsetOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Status">
          <el-select v-model="queryParams.status" placeholder="Please select status" clearable style="width: 150px">
            <el-option label="Active" value="ACTIVE" />
            <el-option label="Paused" value="PAUSED" />
            <el-option label="Deleted" value="DELETED" />
            <el-option label="Pending Review" value="PENDING_REVIEW" />
            <el-option label="Rejected" value="REJECTED" />
          </el-select>
        </el-form-item>
        <el-form-item label="Creative Type">
          <el-select v-model="queryParams.adcreative_type" placeholder="Please select creative type" clearable style="width: 150px">
            <el-option label="Image" value="IMAGE" />
            <el-option label="Video" value="VIDEO" />
            <!--<el-option label="Carousel" value="CAROUSEL" />-->
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>Search
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>Reset
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="sync-container" style="margin-bottom: 15px;">
      <el-button type="primary" @click="handleSyncAds">
        <el-icon><Refresh /></el-icon>Sync Ads
      </el-button>
    </div>

    <!-- Data Table -->
    <table-pagination
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.limit"
      :table-data="adList"
      :loading="loading"
      :total="total"
      @selection-change="handleSelectionChange"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="Ad ID" prop="id" width="180" />
      <el-table-column label="Ad Name" prop="name" min-width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="ad-info" @click="handlePreviewAd(row)">
            <div class="ad-thumbnail" v-if="row.adcreative_type === 'IMAGE' && row.adcreatives[0].image_url">
              <el-image :src="row.adcreatives[0].image_url" fit="cover" class="thumbnail-image" />
            </div>
            <div class="ad-thumbnail" v-else-if="row.adcreative_type === 'VIDEO' && row.adcreatives[0].object_story_spec.video_data.image_url">
              <el-image :src="row.adcreatives[0].object_story_spec.video_data.image_url" fit="cover" class="thumbnail-image">
                <template #error>
                  <div class="thumbnail-placeholder">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="video-icon"><el-icon><VideoPlay /></el-icon></div>
            </div>
            <!--<div class="ad-thumbnail" v-else-if="row.adcreative_type === 'CAROUSEL' && row.carouselItems && row.carouselItems.length">
              <el-image :src="row.carouselItems[0].url" fit="cover" class="thumbnail-image">
                <template #error>
                  <div class="thumbnail-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="carousel-indicator">+{{ row.carouselItems.length }}</div>
            </div>-->
            <div class="ad-name-container">
              <span class="ad-name">{{ row.name }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Ad Set" prop="adset_name" min-width="150" show-overflow-tooltip />
      <el-table-column label="Campaign" prop="campaign_name" min-width="150" show-overflow-tooltip />
      <el-table-column label="Creative Type" prop="adcreative_type" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.adcreative_type === 'IMAGE'" type="success">Image</el-tag>
          <el-tag v-else-if="row.adcreative_type === 'VIDEO'" type="warning">Video</el-tag>
          <el-tag v-else-if="row.adcreative_type === 'CAROUSEL'" type="info">Carousel</el-tag>
          <el-tag v-else-if="row.adcreative_type === 'DYNAMIC'" type="info">Dynamic</el-tag>
          <el-tag v-else type="info">Unknown</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Status" prop="status" width="120" align="center">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="'ACTIVE'"
            :inactive-value="'PAUSED'"
            @change="(val) => handleStatusChange(row.id, val)"
          />
          <span class="status-text">{{ row.status === 'ACTIVE' ? 'Active' : 'Paused' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="EffectiveStatus" prop="effective_status" min-width="130" align="center">
        <template #default="{ row }">
          <span class="status-text">{{ row.effective_status}}</span>
        </template>
      </el-table-column>
      <!--<el-table-column label="Impressions" prop="impressions" width="120" align="center">
        <template #default="{ row }">
          {{ formatNumber(row.statistics?.impressions || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="Clicks" prop="clicks" width="120" align="center">
        <template #default="{ row }">
          {{ formatNumber(row.statistics?.clicks || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="CTR" prop="ctr" width="120" align="center">
        <template #default="{ row }">
          {{ calculateCTR(row.statistics?.impressions, row.statistics?.clicks) }}
        </template>
      </el-table-column>-->
      <el-table-column label="Created Time" prop="created_time" width="180" align="center">
        <template #default="{ row }">
          {{ formatDateTime(row.created_time) }}
        </template>
      </el-table-column>
      <el-table-column label="Actions" width="200" fixed="right">
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button link type="primary" size="small" @click="handlePreviewAd(row)">
              Preview
            </el-button>
            <el-button link type="primary" size="small" @click="handleEditAd(row)">
              Edit
            </el-button>
            <el-dropdown trigger="click" @command="(command) => handleCommand(command, row)">
              <el-button link type="primary" size="small">
                More<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="duplicate">Duplicate Ad</el-dropdown-item>
                  <el-dropdown-item command="delete">Delete Ad</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </table-pagination>

    <!-- Preview Dialog -->
    <el-dialog
      v-model="previewVisible"
      title="Ad Preview"
      width="360px"
      destroy-on-close
      append-to-body
    >
      <div v-if="previewContent" class="preview-container" v-html="previewContent"></div>
      <div v-else class="preview-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>Loading preview...</span>
      </div>
    </el-dialog>

    <!-- Delete Confirmation Dialog -->
    <el-dialog
      v-model="deleteVisible"
      title="Delete Ad"
      width="400px"
      destroy-on-close
      append-to-body
    >
      <div class="delete-confirm">
        <p>Are you sure you want to delete the ad "{{ currentAd?.name }}"? This action cannot be undone.</p>
      </div>
      <template #footer>
        <el-button @click="deleteVisible = false">Cancel</el-button>
        <el-button type="danger" @click="confirmDelete">Confirm Delete</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime, formatNumber } from '@/utils/format'
import TablePagination from '@/components/table/TablePagination.vue'
import {
  getAdList,
  getSyncAds,
  deleteAd,
  updateAdStatus,
  getAdPreview
} from '@/api/ad'
import { getAdsetOptions } from '@/api/adset'

export default {
  name: 'AdList',
  components: {
    TablePagination
  },
  data() {
    return {
      loading: false,
      adList: [],
      total: 0,
      selectedAds: [],
      previewVisible: false,
      deleteVisible: false,
      currentAd: null,
      previewContent: '',
      adsetOptions: [],
      activeTab: 'adList',
      queryParams: {
        page: 1,
        limit: 20,
        name: '',
        id: '',
        adset_id: '',
        status: '',
        adcreative_type: ''
      }
    }
  },
  computed: {
    // 预选的广告组ID（从URL参数中获取）
    preselectedAdsetId() {
      return this.$route.query.adsetId || this.$route.params.adsetId
    },
    preselectedId() {
      return this.$route.query.id || this.$route.params.adsetId
    },
  },
  watch: {
    // 监听预选的广告组ID
    preselectedAdsetId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.adset_id = newVal
        }
      },
      immediate: true
    },
    preselectedId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.id = newVal
        }
      },
      immediate: true
    }
  },
  methods: {
    // 格式化数字
    formatNumber(value) {
      return formatNumber(value)
    },
    
    // 格式化日期时间
    formatDateTime(value) {
      return formatDateTime(value)
    },
    
    // 计算点击率
    calculateCTR(impressions, clicks) {
      if (!impressions || impressions === 0) return '0.00%'
      const ctr = (clicks / impressions) * 100
      return ctr.toFixed(2) + '%'
    },
    
    // 加载广告列表数据
    async loadAdList() {
      this.loading = true
      try {
        const params = {
          page: this.queryParams.page,
          limit: this.queryParams.limit,
          name: this.queryParams.name || undefined,
          id: this.queryParams.id || undefined,
          adset_id: this.queryParams.adset_id || undefined,
          status: this.queryParams.status || undefined,
          adcreative_type: this.queryParams.adcreative_type || undefined
        }
        
        // 调用API获取广告列表
        const res = await getAdList(params)
        if (res.code === 0) {
          this.adList = res.data || []
          this.total = res.total || 0
        } else {
          ElMessage.error(res.msg || 'Failed to get ad list')
        }
      } catch (error) {
        console.error('Failed to get ad list:', error)
        ElMessage.error('Failed to get ad list, please try again later')
      } finally {
        this.loading = false
      }
    },
    
    // 加载广告组选项
    async loadAdsetOptions() {

      const res = await getAdsetOptions()
      if(res.code ===0 ){
        this.adsetOptions = res.data || []
      }

      // 这里应该从API获取广告组列表，使用模拟数据
      /*this.adsetOptions = [
        { value: 'adset_001', label: '核心受众-25-45岁男性' },
        { value: 'adset_002', label: '年轻女性受众-18-30岁' },
        { value: 'adset_003', label: '高收入人群定位' },
        { value: 'adset_004', label: '科技爱好者群体' },
        { value: 'adset_005', label: '游戏玩家群体' },
        { value: 'adset_006', label: '旅游爱好者' },
        { value: 'adset_007', label: '职场人士' },
        { value: 'adset_008', label: '年轻家庭' },
        { value: 'adset_009', label: '健身爱好者' },
        { value: 'adset_010', label: '学生群体' },
        { value: 'adset_011', label: '粉丝重定向' },
        { value: 'adset_012', label: '购物车放弃用户' }
      ]*/
    },
    
    // 搜索按钮点击
    handleSearch() {
      this.queryParams.page = 1
      this.loadAdList()
    },
    
    // 重置搜索表单
    resetQuery() {
      Object.keys(this.queryParams).forEach(key => {
        if (key !== 'page' && key !== 'limit') {
          this.queryParams[key] = ''
        }
      })
      this.handleSearch()
    },
    
    // 创建广告
    handleCreateAd() {
      this.$router.push('/ads/create')
    },
    
    // 编辑广告
    handleEditAd(row) {
      this.$router.push(`/ads/edit/${row.id}`)
    },
    
    // 预览广告
    async handlePreviewAd(row) {
      this.currentAd = row
      this.previewVisible = true
      this.previewContent = ''
      
      try {
        const res = await getAdPreview({id:row.id})
        if (res.code === 0 && res.data) {
          this.previewContent = res.data
        } else {
          ElMessage.error(res.msg || 'Failed to get ad preview')
          this.previewVisible = false
        }
      } catch (error) {
        console.error('Failed to get ad preview:', error)
        ElMessage.error('Failed to get ad preview')
        this.previewVisible = false
      }
    },
    
    // 复制广告
    handleDuplicateAd(row) {
      this.$router.push({
        path: '/ads/create',
        query: { duplicate: row.id }
      })
    },
    
    // 切换广告状态
    async handleStatusChange(id, status) {
      try {
        // 添加确认对话框
        const statusText = status === 'ACTIVE' ? 'activate' : 'pause'
        const confirmMessage = `Are you sure you want to ${statusText} this ad?`
        
        try {
          await ElMessageBox.confirm(confirmMessage, 'Confirm Operation', {
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            type: 'warning'
          })
          
          // 用户确认后执行操作
          const res = await updateAdStatus(id, status)
          if(res.code === 0){
            ElMessage.success('Status updated successfully')
            this.loadAdList()
          }
          else{
            ElMessage.error(res.msg)
          }
        } catch (cancelError) {
          // User cancelled, restore original status
          this.loadAdList() // Refresh list to restore original status
        }
      } catch (error) {
        console.error('Failed to update ad status:', error)
        ElMessage.error('Failed to update ad status')
        this.loadAdList() // 刷新列表，恢复原状态
      }
    },
    
    // 同步广告
    async handleSyncAds() {
      ElMessage.info('Syncing ads...')
      const res = await getSyncAds()
      if(res.code === 0){
        ElMessage.success('Sync ads successfully')
        this.loadAdList()
      }
      else{
        ElMessage.error(res.msg)
      }
    },

    // 删除广告
    handleDeleteAd(row) {
      this.currentAd = row
      this.deleteVisible = true
    },
    
    // 确认删除
    async confirmDelete() {
      try {
        const res = await deleteAd({id:this.currentAd.id})
        if (res.code === 0) {
          ElMessage.success('Ad deleted successfully')
          this.deleteVisible = false
          this.loadAdList()
        } else {
          ElMessage.error(res.msg || 'Failed to delete ad')
        }
      } catch (error) {
        console.error('Failed to delete ad:', error)
        ElMessage.error('Failed to delete ad, please try again later')
      }
    },
    
    // 下拉菜单命令处理
    handleCommand(command, row) {
      switch (command) {
        case 'duplicate':
          this.handleDuplicateAd(row)
          break
        case 'toggleStatus':
          this.handleToggleStatus(row)
          break
        case 'delete':
          this.handleDeleteAd(row)
          break
        default:
          break
      }
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedAds = selection
    },
    
    // 分页大小变化
    handleSizeChange(size) {
      this.queryParams.limit = size
      this.loadAdList()
    },
    
    // 当前页变化
    handleCurrentChange(page) {
      this.queryParams.page = page
      this.loadAdList()
    }
  },
  mounted() {
    this.loadAdList()
    this.loadAdsetOptions()
  }
}
</script>

<style lang="scss" scoped>
.ad-list-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }
  }
  
  .filter-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      
      .el-form-item {
        margin-bottom: 10px;
        margin-right: 15px;
      }
    }
  }

  .ad-info {
    cursor: pointer;
    display: flex;
    align-items: center;
    
    .ad-thumbnail {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 10px;
      position: relative;
      background-color: #f5f7fa;
      
      .thumbnail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .thumbnail-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
      }
      
      .video-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }
      
      .carousel-indicator {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 10px;
        padding: 2px 4px;
        border-top-left-radius: 4px;
      }
    }
    
    .ad-name-container {
      flex: 1;
      overflow: hidden;
      
      .ad-name {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .delete-confirm {
    text-align: center;
    margin: 20px 0;
    
    p {
      margin: 0;
    }
  }
  
  .operation-buttons {
    display: flex;
    align-items: center;
    
    .el-button {
      padding: 2px 6px;  // 保持按钮内边距
      height: 24px;      // 保持按钮高度
      line-height: 1;    // 确保文字垂直居中
      
      &:not(:last-child) {
        margin-right: 4px;  // 预览和编辑按钮之间的间距
      }
    }
    
    .el-dropdown {
      margin-left: 12px;    // 增加"更多"按钮的左边距
    }
  }
  
  .status-text {
    margin-left: 8px;
    font-size: 12px;
  }
  
  .preview-container {
    width: 100%;
    min-height: 450px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .preview-loading {
    width: 100%;
    height: 450px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    .el-icon {
      font-size: 24px;
      margin-bottom: 10px;
    }
    
    span {
      color: #909399;
      font-size: 14px;
    }
  }
}
</style> 