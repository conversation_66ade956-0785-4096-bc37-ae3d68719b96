<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item 
      v-for="(item, index) in breadcrumbs" 
      :key="item.path"
      :to="index < breadcrumbs.length - 1 ? { path: item.path } : null"
    >
      {{ item.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const breadcrumbs = ref([])

// 生成面包屑数据
const getBreadcrumbs = () => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  
  // 如果不是Dashboard开始，添加首页
  if (matched.length && matched[0].name !== 'Dashboard') {
    matched.unshift({
      path: '/',
      meta: { title: 'Home' }
    })
  }
  
  breadcrumbs.value = matched.map(item => {
    return {
      path: item.path,
      title: item.meta.title
    }
  })
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    getBreadcrumbs()
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.el-breadcrumb {
  display: inline-block;
  line-height: 60px;
}
</style> 