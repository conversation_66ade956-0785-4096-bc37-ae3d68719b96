<template>
  <div class="fb-ads-pixels-container">
    <!-- Page title and action bar -->
    <div class="page-header">
      <div class="header-left">
        <h2>FB Ads Pixels List</h2>
        <div class="business-info" v-if="businessName">
          <el-tag type="info">Business: {{ businessName }}</el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          Back to Business List
        </el-button>
        <el-button type="primary" @click="refreshList">
          <el-icon><Refresh /></el-icon>
          Refresh
        </el-button>
      </div>
    </div>

    <!-- Search bar -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="Pixel Name">
          <el-input
            v-model="searchForm.name"
            placeholder="Enter pixel name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="Status">
          <el-select
            v-model="searchForm.status"
            placeholder="Select status"
            clearable
            style="width: 150px;"
          >
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Type">
          <el-select
            v-model="searchForm.type"
            placeholder="Select type"
            clearable
            style="width: 150px;"
          >
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
          <el-button @click="resetSearch">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Pixels list table -->
    <div class="table-container">
      <TablePagination
        :table-data="pixelsList"
        :loading="loading"
        :total="pagination.total"
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="Pixel ID" min-width="150" />
        
        <el-table-column prop="name" label="Pixel Info" min-width="200">
          <template #default="{ row }">
            <div class="pixel-info">
              <el-avatar 
                :size="40" 
                :src="generatePixelAvatar(row.name)"
                shape="square"
                class="pixel-avatar"
              >
                <el-icon><DataAnalysis /></el-icon>
              </el-avatar>
              <div class="pixel-details">
                <div class="pixel-name">{{ row.name }}</div>
                <div class="pixel-id">FB ID: {{ row.pixelId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="Type" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" size="medium">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="Status" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="medium"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="Events" min-width="120">
          <template #default="{ row }">
            <div class="events-info">
              <div class="events-count">{{ formatNumber(row.eventsCount) }} Events</div>
              <div class="conversions-count">{{ formatNumber(row.conversionsCount) }} Conversions</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="Verification" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.isVerified ? 'success' : 'warning'"
              size="small"
            >
              {{ row.isVerified ? 'Verified' : 'Unverified' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="createOn"
          label="Creation Date"
          align="center"
          min-width="150"
        >
          <template #default="{ row }">
            {{ formatDate(row.createOn) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="lastUpdateTime"
          label="Last Update Time"
          align="center"
          min-width="150"
        >
          <template #default="{ row }">
            {{ formatDate(row.lastUpdateTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" min-width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewPixelDetail(row)"
            >
              View Detail
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewPixelEvents(row)"
            >
              Events
            </el-button>
          </template>
        </el-table-column>
      </TablePagination>
    </div>
  </div>
</template>

<script>
import { getFBAdsPixelsList } from '@/api/fbadspixels'
import { ElMessage } from 'element-plus'
import { Refresh, ArrowLeft, DataAnalysis } from '@element-plus/icons-vue'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'FBAdsPixelsList',
  components: {
    Refresh,
    ArrowLeft,
    DataAnalysis,
    TablePagination
  },
  data() {
    return {
      loading: false,
      pixelsList: [],
      selectedPixels: [],
      businessId: '',
      businessName: '',
      searchForm: {
        name: '',
        status: '',
        type: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      statusOptions: [
        { label: 'Active', value: 'ACTIVE' },
        { label: 'Inactive', value: 'INACTIVE' },
        { label: 'Pending', value: 'PENDING' }
      ],
      typeOptions: [
        { label: 'Website', value: 'WEBSITE' },
        { label: 'App', value: 'APP' },
        { label: 'Offline', value: 'OFFLINE' }
      ]
    }
  },
  mounted() {
    this.initializeFromQuery()
    this.fetchPixelsList()
  },
  methods: {
    // Initialize from query parameters
    initializeFromQuery() {
      const query = this.$route.query
      this.businessId = query.businessId || ''
      this.businessName = query.businessName || ''
    },

    // Fetch pixels list
    async fetchPixelsList() {
      this.loading = true
      try {
        const params = {
          businessId: this.businessId,
          page: this.pagination.page,
          limit: this.pagination.size,
          ...this.searchForm
        }
        
        const response = await getFBAdsPixelsList(params)
        
        if (response.code === 0) {
          this.pixelsList = response.data || []
          this.pagination.total = response.total || 0
        } else {
          ElMessage.error(response.message || 'Failed to fetch pixels list')
        }
      } catch (error) {
        console.error('Failed to fetch pixels list:', error)
        ElMessage.error('Failed to fetch pixels list')
      } finally {
        this.loading = false
      }
    },

    // Search
    handleSearch() {
      this.pagination.page = 1
      this.fetchPixelsList()
    },

    // Reset search
    resetSearch() {
      this.searchForm = {
        name: '',
        status: '',
        type: ''
      }
      this.pagination.page = 1
      this.fetchPixelsList()
    },

    // Refresh list
    refreshList() {
      this.fetchPixelsList()
    },

    // Go back
    goBack() {
      this.$router.push({ name: 'FBBusinessList' })
    },

    // Selection change
    handleSelectionChange(selection) {
      this.selectedPixels = selection
    },

    // Page size change
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchPixelsList()
    },

    // Current page change
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchPixelsList()
    },

    // View pixel detail
    viewPixelDetail(pixel) {
      ElMessage.info(`View detail for pixel: ${pixel.name}`)
      // TODO: Navigate to pixel detail page
    },

    // View pixel events
    viewPixelEvents(pixel) {
      ElMessage.info(`View events for pixel: ${pixel.name}`)
      // TODO: Navigate to pixel events page
    },

    // Generate pixel avatar URL
    generatePixelAvatar(name) {
      const seed = encodeURIComponent(name || 'default')
      return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=67c23a&textColor=ffffff`
    },

    // Get status type for tag
    getStatusType(status) {
      const statusMap = {
        'ACTIVE': 'success',
        'INACTIVE': 'danger',
        'PENDING': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // Get status text
    getStatusText(status) {
      const statusMap = {
        'ACTIVE': 'Active',
        'INACTIVE': 'Inactive',
        'PENDING': 'Pending'
      }
      return statusMap[status] || status
    },

    // Get type tag type
    getTypeTagType(type) {
      const typeMap = {
        'WEBSITE': 'primary',
        'APP': 'success',
        'OFFLINE': 'warning'
      }
      return typeMap[type] || 'info'
    },

    // Get type label
    getTypeLabel(type) {
      const typeMap = {
        'WEBSITE': 'Website',
        'APP': 'App',
        'OFFLINE': 'Offline'
      }
      return typeMap[type] || type
    },

    // Format number
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },

    // Format date
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.fb-ads-pixels-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    .business-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;

  .el-form {
    margin: 0;
  }
}

.table-container {
  margin-bottom: 20px;
}

.pixel-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .pixel-avatar {
    flex-shrink: 0;
  }

  .pixel-details {
    flex: 1;
    min-width: 0;

    .pixel-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .pixel-id {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.events-info {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .events-count {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
  }

  .conversions-count {
    font-size: 12px;
    color: #909399;
  }
}

// Responsive design
@media (max-width: 768px) {
  .fb-ads-pixels-container {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .header-left {
      h2 {
        font-size: 20px;
      }
    }
  }

  .search-bar {
    .el-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;

        .el-input, .el-select {
          width: 100%;
        }
      }
    }
  }

  .pixel-info {
    .pixel-avatar {
      width: 32px;
      height: 32px;
    }

    .pixel-details {
      .pixel-name {
        font-size: 14px;
      }

      .pixel-id {
        font-size: 11px;
      }
    }
  }

  .events-info {
    .events-count {
      font-size: 12px;
    }

    .conversions-count {
      font-size: 10px;
    }
  }

  // Mobile operation button styles
  :deep(.el-table__fixed-right) {
    .el-button {
      margin: 2px;
      padding: 5px 8px;
      font-size: 12px;

      &.el-button--small {
        padding: 4px 6px;
      }
    }
  }
}
</style>
