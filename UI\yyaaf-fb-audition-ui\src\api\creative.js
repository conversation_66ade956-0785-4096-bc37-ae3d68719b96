import request from '@/utils/request'

/**
 * 获取广告创意列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getCreativeList(params) {
  return request({
    url: '/creatives',
    method: 'get',
    params
  })
}

/**
 * 获取广告创意详情
 * @param {string} id - 创意ID
 * @returns {Promise}
 */
export function getCreativeDetail(params) {
  return request({
    url: `/creatives/detail`,
    method: 'get',
    params
  })
}

/**
 * 创建广告创意
 * @param {Object} data - 创意数据
 * @returns {Promise}
 */
export function createCreative(data) {
  return request({
    url: '/creatives/create',
    method: 'post',
    data
  })
}

/**
 * 更新广告创意
 * @param {string} id - 创意ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateCreative(id, data) {
  return request({
    url: `/creatives/update/${id}`,
    method: 'post',
    data
  })
}

/**
 * 删除广告创意
 * @param {string} id - 创意ID
 * @returns {Promise}
 */
export function deleteCreative(data) {
  return request({
    url: `/creatives/delete`,
    method: 'post',
    data
  })
}

/**
 * 预览广告创意
 * @param {Object} data - 创意数据
 * @returns {Promise}
 */
export function previewCreative(data) {
  return request({
    url: '/creatives/preview',
    method: 'post',
    data
  })
}
