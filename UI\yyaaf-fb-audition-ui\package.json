{"name": "yyaaf-fb-audition-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue/compiler-sfc": "^3.2.13", "axios": "^1.8.4", "core-js": "^3.8.3", "echarts": "^5.4.3", "element-plus": "^2.5.6", "mockjs": "^1.1.0", "pinia": "^2.0.11", "vue": "^3.2.13", "vue-router": "^4.0.3", "vue-template-compiler": "^2.7.16", "vuex": "^4.0.2", "crypto-js": "^4.0.0", "jsencrypt": "^3.0.0-rc.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.7.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-loader": "^17.4.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}