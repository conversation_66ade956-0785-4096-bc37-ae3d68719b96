import store from '@/store'

const TokenKey = 'admin_token'
const TokenExpireKey = 'admin_token_expire'
const hour = 60 * 60 * 1000
const day = 24 * hour
const defaultExpire = 7 * day // 默认7天过期

/**
 * 获取token
 * @returns {string|null} token值,如果已过期返回null
 */
export function getToken() {
  try {
    const token = localStorage.getItem(TokenKey)
    const expireTime = localStorage.getItem(TokenExpireKey)
    
    if (!token || !expireTime) {
      return null
    }

    // 检查是否过期
    if (new Date().getTime() > parseInt(expireTime)) {
      removeToken()
      return null
    }

    return token
  } catch (error) {
    console.error('获取token失败:', error)
    return null
  }
}

/**
 * 设置token
 * @param {string} token - token值
 * @param {number} [expire] - 过期时间(毫秒),默认7天
 */
export function setToken(token, expire = defaultExpire) {
  try {
    const expireTime = new Date().getTime() + expire
    localStorage.setItem(TokenKey, token)
    localStorage.setItem(TokenExpireKey, expireTime.toString())
  } catch (error) {
    console.error('设置token失败:', error)
  }
}

/**
 * 移除token
 */
export function removeToken() {
  try {
    localStorage.removeItem(TokenKey)
    localStorage.removeItem(TokenExpireKey)
  } catch (error) {
    console.error('移除token失败:', error)
  }
}

/**
 * 检查token是否过期
 * @returns {boolean} 是否过期
 */
export function isTokenExpired() {
  try {
    const expireTime = localStorage.getItem(TokenExpireKey)
    if (!expireTime) {
      return true
    }
    return new Date().getTime() > parseInt(expireTime)
  } catch (error) {
    console.error('检查token过期失败:', error)
    return true
  }
}

/**
 * 获取token过期时间
 * @returns {number|null} 过期时间戳,如果没有token返回null
 */
export function getTokenExpireTime() {
  try {
    const expireTime = localStorage.getItem(TokenExpireKey)
    return expireTime ? parseInt(expireTime) : null
  } catch (error) {
    console.error('获取token过期时间失败:', error)
    return null
  }
}

/**
 * 刷新token过期时间
 * @param {number} [expire] - 新的过期时间(毫秒),默认7天
 */
export function refreshTokenExpire(expire = defaultExpire) {
  try {
    const token = localStorage.getItem(TokenKey)
    if (token) {
      setToken(token, expire)
    }
  } catch (error) {
    console.error('刷新token过期时间失败:', error)
  }
}