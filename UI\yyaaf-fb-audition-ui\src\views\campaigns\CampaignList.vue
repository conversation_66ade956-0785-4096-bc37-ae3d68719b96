<template>
  <div class="campaign-list-container">
    <div class="campaign-header">
      <h2>Campaign Management</h2>
      <div class="campaign-actions">
        <el-button type="primary" @click="goToCreate">Create Campaign</el-button>
        <el-button type="danger" :disabled="!hasSelected" @click="handleBatchDelete">Batch Delete</el-button>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="Campaign Name">
          <el-input v-model="searchForm.name" placeholder="Please enter campaign name" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="Campaign ID">
          <el-input v-model="searchForm.id" placeholder="Please enter campaign ID" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="Status">
          <el-select 
            v-model="searchForm.status" 
            placeholder="Please select status" 
            clearable
            style="width: 200px"
          >
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Created Date">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="to"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
          <el-button @click="resetSearch">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="sync-container" style="margin-bottom: 15px;">
      <el-button type="primary" @click="handleSyncCampaigns">
        <el-icon><Refresh /></el-icon>Sync Campaigns
      </el-button>
    </div>
    
    <!-- Table Pagination Component -->
    <table-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :table-data="campaignList"
      :loading="loading"
      :total="pagination.total"
      @selection-change="handleSelectionChange"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="Campaign ID" width="180" />
      <el-table-column prop="name" label="Campaign Name" min-width="180" show-overflow-tooltip />
      <el-table-column prop="objective" label="Objective" min-width="120">
        <template #default="scope">
          <el-tag>{{ formatObjective(scope.row.objective) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="daily_budget" label="Daily Budget" width="120">
        <template #default="scope">
          {{ formatCurrency(scope.row.daily_budget) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="Status" width="120">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="'ACTIVE'"
            :inactive-value="'PAUSED'"
            @change="(val) => handleStatusChange(scope.row.id, val)"
          />
          <span class="status-text">{{ scope.row.status === 'ACTIVE' ? 'Active' : 'Paused' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="start_time" label="Start Time" width="180" />
      <el-table-column prop="stop_time" label="End Time" width="180" />
      <el-table-column prop="created_time" label="Created Time" width="180" />
      <el-table-column label="Actions" min-width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="goToEdit(scope.row.id)">Edit</el-button>
          <el-button type="primary" link @click="goToAdsets(scope.row.id)">Ad Sets</el-button>
          <el-popconfirm title="Are you sure you want to delete this campaign?" @confirm="handleDelete(scope.row.id)">
            <template #reference>
              <el-button type="danger" link>Delete</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </table-pagination>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCampaignList, deleteCampaign, updateCampaignStatus, getSyncCampaigns } from '@/api/campaign'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'CampaignList',
  
  components: {
    TablePagination
  },
  
  data() {
    return {
      // Status options
      statusOptions: [
        { value: 'ACTIVE', label: 'Active' },
        { value: 'PAUSED', label: 'Paused' },
        { value: 'ARCHIVED', label: 'Archived' }
      ],
      
      // Table data
      campaignList: [],
      loading: false,
      selectedRows: [],
      
      // Search conditions
      searchForm: {
        name: '',
        id: '',
        status: '',
        dateRange: []
      },
      
      // Pagination info
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  
  computed: {
    // Whether any row is selected
    hasSelected() {
      return this.selectedRows.length > 0
    }
  },
  
  methods: {
    // Format campaign objective
    formatObjective(objective) {
      const objectiveMap = {
        'OUTCOME_TRAFFIC': 'Drive Traffic',
        'OUTCOME_LEADS': 'Leads',
        'OUTCOME_SALES': 'Sales',
        'OUTCOME_ENGAGEMENT': 'Engagement',
        'OUTCOME_AWARENESS': 'Awareness',
        'OUTCOME_APP_PROMOTION': 'App Promotion',
      }
      return objectiveMap[objective] || objective
    },
    
    // Format currency
    formatCurrency(value) {
      if (!value || value == 0) return 'Ad Set Budget'
      return `$${Number(value).toFixed(2)}`
    },
    
    // Fetch campaign list
    async fetchCampaignList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize,
          name: this.searchForm.name || undefined,
          id: this.searchForm.id || undefined,
          status: this.searchForm.status || undefined
        }
        
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.beginTime = this.searchForm.dateRange[0]
          params.endTime = this.searchForm.dateRange[1]
        }

        const res = await getCampaignList(params)
        if(res.code ===0 ){
          this.campaignList = res.data
          this.pagination.total = res.total
        }
        else{
          ElMessage.error(res.msg)
        }
      } catch (error) {
        console.error('Failed to fetch campaign list:', error)
        ElMessage.error('Failed to fetch campaign list')
      } finally {
        this.loading = false
      }
    },
    
    // Handle search
    handleSearch() {
      this.pagination.currentPage = 1
      this.fetchCampaignList()
    },
    
    // Reset search
    resetSearch() {
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = key === 'dateRange' ? [] : ''
      })
      this.pagination.currentPage = 1
      this.fetchCampaignList()
    },
    
    // Page change
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.fetchCampaignList()
    },
    
    // Page size change
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.fetchCampaignList()
    },
    
    // Selection change
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },
    
    // Go to create page
    goToCreate() {
      this.$router.push('/campaigns/create')
    },
    
    // Go to edit page
    goToEdit(id) {
      this.$router.push(`/campaigns/edit/${id}`)
    },
    
    // Go to ad sets page
    goToAdsets(id) {
      this.$router.push(`/adsets/list?campaignId=${id}`)
    },
    
    // Delete campaign
    async handleDelete(id) {
      try {
        await deleteCampaign({id:id})
        ElMessage.success('Deleted successfully')
      } catch (error) {
        console.error('Failed to delete campaign:', error)
        ElMessage.error('Failed to delete campaign')
      }
    },
    
    // Batch delete
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        return ElMessage.warning('Please select campaigns to delete first')
      }

      ElMessageBox.confirm('Are you sure you want to batch delete the selected campaigns?', 'Confirm Delete', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(async () => {
        const ids = this.selectedRows.map(item => item.id)
        
        try {
          const res = await Promise.all(ids.map(id => deleteCampaign({id:id})))       
          // Check if all deletions succeeded
          const allSuccess = res.every(item => item.code === 0)
          if (allSuccess) {
            ElMessage.success('Batch delete successful')
          } else {
            // Some succeeded, some failed
            const successCount = res.filter(item => item.code === 0).length
            ElMessage.warning(`Partial success: ${successCount}/${res.length} deleted`)
          }
          this.fetchCampaignList()
        } catch (error) {
          console.error('Failed to batch delete campaigns:', error)
          ElMessage.error('Failed to batch delete campaigns')
        }
      }).catch(() => {
        // Cancelled, do nothing
      })
    },
    
    // Change campaign status
    async handleStatusChange(id, status) {
      const newStatus = status === 'ACTIVE' ? 'Active' : 'Paused'
      const oldStatus = status === 'ACTIVE' ? 'Paused' : 'Active'
      
      try {
        await ElMessageBox.confirm(
          `Are you sure you want to change the campaign status from ${oldStatus} to ${newStatus}?`,
          'Confirm Status Change',
          {
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            type: 'warning'
          }
        )
        
        await updateCampaignStatus(id, status)
        ElMessage.success('Status updated successfully')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to update campaign status:', error)
          ElMessage.error('Failed to update campaign status')
          this.fetchCampaignList() // Refresh list to restore original status
        }
      }
    },
    
    // 同步广告系列
    async handleSyncCampaigns() {
      ElMessage.info('Syncing campaigns...')
      const res = await getSyncCampaigns()
      if(res.code === 0){
        ElMessage.success('Sync campaigns successfully')
        this.fetchCampaignList()
      }
      else{
        ElMessage.error(res.msg)
      }
    }
  },
  
  mounted() {
    this.fetchCampaignList()
  }
}
</script>

<style scoped>
.campaign-list-container {
  padding: 20px;
}

.campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.status-text {
  margin-left: 8px;
  font-size: 12px;
}
</style> 