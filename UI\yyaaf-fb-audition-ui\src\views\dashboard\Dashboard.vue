<template>
  <div class="dashboard-container">
    <!-- Date Query Area -->
    <el-card shadow="hover" class="date-filter-card">
      <div class="date-filter">
        <div class="filter-item">
          <span class="label">Select Date:</span>
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="Select Date"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            :disabled-date="disabledDate"
            @change="handleDateChange"
          />
        </div>
        <div class="filter-actions">
          <el-button type="primary" @click="fetchDashboardData">Query</el-button>
          <el-button @click="resetDateRange">Reset</el-button>
        </div>
      </div>
    </el-card>
    
    <!-- Data Statistics Cards -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="statistic-card" v-loading="loading.overview">
          <template #header>
            <div class="card-header">
              <span>Today's Impressions</span>
              <el-tooltip content="Total ad impressions today" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="statistic-value">
            <span class="value">{{ overview.impressions || 0 }}</span>
            <div class="trend" :class="overview.impressionsTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.impressionsTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              <span>{{ Math.abs(overview.impressionsTrend || 0) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="statistic-card" v-loading="loading.overview">
          <template #header>
            <div class="card-header">
              <span>Today's Clicks</span>
              <el-tooltip content="Total ad clicks today" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="statistic-value">
            <span class="value">{{ overview.clicks || 0 }}</span>
            <div class="trend" :class="overview.clicksTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.clicksTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              <span>{{ Math.abs(overview.clicksTrend || 0) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="statistic-card" v-loading="loading.overview">
          <template #header>
            <div class="card-header">
              <span>Today's Conversions</span>
              <el-tooltip content="Total ad conversions today" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="statistic-value">
            <span class="value">{{ overview.conversions || 0 }}</span>
            <div class="trend" :class="overview.conversionsTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.conversionsTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              <span>{{ Math.abs(overview.conversionsTrend || 0) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="statistic-card" v-loading="loading.overview">
          <template #header>
            <div class="card-header">
              <span>Today's Spend</span>
              <el-tooltip content="Total ad spend today" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="statistic-value">
            <span class="value">${{ overview.todaySpend || 0 }}</span>
            <div class="trend" :class="overview.todaySpendTrend > 0 ? 'up' : 'down'">
              <el-icon v-if="overview.todaySpendTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
              <span>{{ Math.abs(overview.todaySpendTrend || 0) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- Chart Area -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="16">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>Ad Spend Trend</span>
              <div class="chart-actions">
                <el-radio-group v-model="trendTimeRange" size="small">
                  <el-radio-button label="week">This Week</el-radio-button>
                  <el-radio-button label="month">This Month</el-radio-button>
                  <el-radio-button label="year">This Year</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="trendChartRef" v-loading="loading.trends"></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>Ad Type Distribution</span>
            </div>
          </template>
          <div class="chart-container" ref="pieChartRef" v-loading="loading.adTypes"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- Recent Ad Campaigns -->
    <el-card shadow="hover" class="recent-activity">
      <template #header>
        <div class="card-header">
          <span>Recent Ad Campaigns</span>
          <el-button type="primary" size="small" plain @click="$router.push('/campaigns/list')">
            View All
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentCampaigns" style="width: 100%" stripe v-loading="loading.recentCampaigns">
        <el-table-column prop="name" label="Campaign Name" min-width="180" />
        <el-table-column prop="objective" label="Ad Objective" min-width="180" />
        <el-table-column prop="status" label="Status" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ACTIVE' ? 'success' : row.status === 'PAUSED' ? 'warning' : 'info'">
              {{ row.status === 'ACTIVE' ? 'Running' : row.status === 'PAUSED' ? 'Paused' : 'Stopped' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="daily_budget" label="Budget" width="120">
          <template #default="scope">
          {{ formatCurrency(scope.row.daily_budget) }}
        </template>
        </el-table-column>
        <el-table-column prop="created_time" label="Creation Time" width="180" />
        <el-table-column label="Action" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="$router.push(`/campaigns/edit/${row.id}`)">
              Edit
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  getDashboardOverview, 
  getAdTrends, 
  getAdTypeDistribution, 
  getDashboardData
} from '@/api/dashboard'

import { getCampaignList } from '@/api/campaign'

export default {
  name: 'Dashboard',
  data() {
    // Get current date
    const today = new Date()
    const year = today.getFullYear()
    const month = today.getMonth()
    const day = today.getDate()
    
    // Default selection is the last 7 days
    const defaultStartDate = new Date(year, month, day - 6)
    const defaultEndDate = today
    
    // Format date as YYYY-MM-DD
    const formatDate = (date) => {
      const y = date.getFullYear()
      const m = (date.getMonth() + 1).toString().padStart(2, '0')
      const d = date.getDate().toString().padStart(2, '0')
      return `${y}-${m}-${d}`
    }
    
    return {
      // Date selection
      selectedDate: formatDate(today),
      
      // Date shortcuts
      dateShortcuts: [
        {
          text: 'Today',
          value: () => {
            return new Date()
          },
        },
        {
          text: 'Yesterday',
          value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)
            return date
          },
        },
        {
          text: 'A week ago',
          value: () => {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
            return date
          },
        },
      ],
      
      // Chart instances
      trendChart: null,
      pieChart: null,
      
      // Time range switch
      trendTimeRange: 'month',
      
      // Loading status
      loading: {
        overview: false,
        trends: false,
        adTypes: false,
        recentCampaigns: false
      },
      
      // Overview data
      overview: {
        impressions: 0,
        impressionsTrend: 0,
        clicks: 0,
        clicksTrend: 0,
        conversions: 0,
        conversionsTrend: 0,
        todaySpend: 0,
        todaySpendTrend: 0
      },
      
      // Recent ad campaigns data
      recentCampaigns: []
    }
  },
  
  computed: {
    // Get date parameters
    dateParams() {
      if (!this.selectedDate) {
        return {}
      }
      
      return {
        Date: this.selectedDate
      }
    }
  },
  
  watch: {
    // Listen for time range changes
    trendTimeRange: {
      handler(newVal) {
        this.fetchAdTrends()
      }
    }
  },
  
  mounted() {
    // Load dashboard data
    this.fetchDashboardData()
    
    // Add window resize event listener
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeUnmount() {
    // Clean up event listeners and chart instances before component unmounts
    window.removeEventListener('resize', this.handleResize)
    if (this.trendChart) {
      this.trendChart.dispose()
      this.trendChart = null
    }
    if (this.pieChart) {
      this.pieChart.dispose()
      this.pieChart = null
    }
  },
  
  methods: {
    // Disable future dates
    disabledDate(time) {
      return time.getTime() > Date.now()
    },
    
    // Handle date change
    handleDateChange(val) {
      console.log('Selected date is:', val)
    },
    
    // Reset date range
    resetDateRange() {
      const today = new Date()
      
      const formatDate = (date) => {
        const y = date.getFullYear()
        const m = (date.getMonth() + 1).toString().padStart(2, '0')
        const d = date.getDate().toString().padStart(2, '0')
        return `${y}-${m}-${d}`
      }
      
      this.selectedDate = formatDate(today)
      this.fetchDashboardData()
    },
    
    // Get all dashboard data
    async fetchDashboardData() {
      try {
        // Request multiple data
        this.fetchOverviewData()
        this.fetchAdTrends()
        this.fetchAdTypeDistribution()
        this.fetchRecentCampaigns()
        
        // You can also request all data at once
        // this.loading.overview = true
        // this.loading.trends = true
        // this.loading.adTypes = true
        // this.loading.recentCampaigns = true
        
        // const res = await getDashboardData({
        //   timeRange: this.trendTimeRange,
        //   ...this.dateParams
        // })
        
        // Process data
        // this.overview = res.overview || this.overview
        // this.recentCampaigns = res.recentCampaigns || []
        
        // if (res.trends) {
        //   this.renderTrendChart(res.trends)
        // }
        
        // if (res.adTypes) {
        //   this.renderPieChart(res.adTypes)
        // }
      } catch (error) {
        console.error('Failed to get dashboard data:', error)
        ElMessage.error('Failed to get dashboard data')
      } finally {
        // this.loading.overview = false
        // this.loading.trends = false
        // this.loading.adTypes = false
        // this.loading.recentCampaigns = false
      }
    },
    
    // Get overview data
    async fetchOverviewData() {
      this.loading.overview = true
      try {
        const res = await getDashboardOverview(this.dateParams)
        this.overview = res.data || this.overview
      } catch (error) {
        console.error('Failed to get overview data:', error)
      } finally {
        this.loading.overview = false
      }
    },
    
    // Get ad spend trend data
    async fetchAdTrends() {
      this.loading.trends = true
      try {
        const res = await getAdTrends(this.trendTimeRange, this.dateParams)
        this.renderTrendChart(res.data)
      } catch (error) {
        console.error('Failed to get ad spend trend data:', error)
        // If API request fails, use mock data
        this.initTrendChart()
      } finally {
        this.loading.trends = false
      }
    },
    
    // Get ad type distribution data
    async fetchAdTypeDistribution() {
      this.loading.adTypes = true
      try {
        const res = await getAdTypeDistribution(this.dateParams)
        this.renderPieChart(res.data)
      } catch (error) {
        console.error('Failed to get ad type distribution data:', error)
        // If API request fails, use mock data
        this.initPieChart()
      } finally {
        this.loading.adTypes = false
      }
    },
    
    // Get recent ad campaigns
    async fetchRecentCampaigns() {
      this.loading.recentCampaigns = true
      try {
        const params = {
          page: 1,
          limit: 5,
          ...this.dateParams
        }
        const res = await getCampaignList(params)
        this.recentCampaigns = res.data || []
      } catch (error) {
        console.error('Failed to get recent ad campaigns:', error)
      } finally {
        this.loading.recentCampaigns = false
      }
    },

    // Render ad spend trend chart
    renderTrendChart(data) {
      if (!this.$refs.trendChartRef) return
      
      if (this.trendChart) {
        this.trendChart.dispose()
      }
      
      this.trendChart = echarts.init(this.$refs.trendChartRef)
      this.trendChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data?.xAxis || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'Ad Spend',
            type: 'line',
            data: data?.series || [],
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.6)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ]
              }
            },
            itemStyle: {
              color: '#409EFF'
            },
            smooth: true
          }
        ]
      })
    },
    
    // Render ad type distribution chart
    renderPieChart(data) {
      if (!this.$refs.pieChartRef) return
      
      if (this.pieChart) {
        this.pieChart.dispose()
      }
      
      this.pieChart = echarts.init(this.$refs.pieChartRef)
      this.pieChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: data?.legendData || []
        },
        series: [
          {
            name: 'Ad Type',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data?.seriesData || []
          }
        ]
      })
    },
    
    // Initialize ad spend trend chart (using mock data)
    initTrendChart() {
      if (!this.$refs.trendChartRef) return
      
      // Generate different mock data based on time range
      const xData = []
      const yData = []
      
      if (this.trendTimeRange === 'week') {
        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for (let i = 0; i < 7; i++) {
          xData.push(days[i])
          yData.push(Math.floor(Math.random() * 50) + 50)
        }
      } else if (this.trendTimeRange === 'month') {
        for (let i = 1; i <= 30; i++) {
          xData.push(i + 'th')
          yData.push(Math.floor(Math.random() * 80) + 20)
        }
      } else {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        for (let i = 0; i < 12; i++) {
          xData.push(months[i])
          yData.push(Math.floor(Math.random() * 100) + 100)
        }
      }
      
      this.renderTrendChart({
        xAxis: xData,
        series: yData
      })
    },
    
    // Initialize ad type distribution chart (using mock data)
    initPieChart() {
      if (!this.$refs.pieChartRef) return
      
      const legendData = ['Image Ad', 'Video Ad', 'Carousel Ad', 'Dynamic Ad', 'Link Ad']
      const seriesData = [
        { value: 35, name: 'Image Ad' },
        { value: 28, name: 'Video Ad' },
        { value: 15, name: 'Carousel Ad' },
        { value: 12, name: 'Dynamic Ad' },
        { value: 10, name: 'Link Ad' }
      ]
      
      this.renderPieChart({
        legendData,
        seriesData
      })
    },
    
    // Redraw charts on window resize
    handleResize() {
      if (this.trendChart) {
        this.trendChart.resize()
      }
      if (this.pieChart) {
        this.pieChart.resize()
      }
    },

    // Format currency
    formatCurrency(value) {
      if (!value || value == 0) return 'Ad Set Budget'
      return `$${Number(value).toFixed(2)}`
    },
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  .date-filter-card {
    margin-bottom: 20px;
    
    .date-filter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .filter-item {
        display: flex;
        align-items: center;
        
        .label {
          margin-right: 10px;
          font-weight: bold;
        }
      }
      
      .filter-actions {
        button {
          margin-left: 10px;
        }
      }
    }
  }
  
  .statistic-card {
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .el-icon {
        font-size: 16px;
        color: #909399;
        cursor: pointer;
      }
    }
    
    .statistic-value {
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      
      .value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
      }
      
      .trend {
        display: flex;
        align-items: center;
        font-size: 14px;
        
        &.up {
          color: #67c23a;
        }
        
        &.down {
          color: #f56c6c;
        }
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  .chart-row {
    margin-top: 20px;
  }
  
  .chart-container {
    height: 350px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .recent-activity {
    margin-top: 20px;
  }
}
</style> 