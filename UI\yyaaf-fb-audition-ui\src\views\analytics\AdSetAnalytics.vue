<template>
  <div class="analytics-container">
    <div class="header-section">
      <h2>Ad Set Analytics</h2>
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="Date Range">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="To"
              start-placeholder="Start Date"
              end-placeholder="End Date"
              value-format="YYYY-MM-DD"
              @change="fetchAnalyticsData"
            />
          </el-form-item>
          <el-form-item label="Campaign">
            <el-select
              v-model="filterForm.campaignId"
              placeholder="Select Campaign"
              filterable
              clearable
              @change="handleCampaignChange"
            >
              <el-option
                v-for="item in campaignOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Ad Set">
            <el-select
              v-model="filterForm.adsetIds"
              multiple
              collapse-tags
              placeholder="Select Ad Set"
              filterable
              clearable
            >
              <el-option
                v-for="item in adsetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!--<el-form-item label="Status">
            <el-select
              v-model="filterForm.status"
              placeholder="Select Status"
              clearable
            >
              <el-option label="Active" value="ACTIVE" />
              <el-option label="Paused" value="PAUSED" />
              <el-option label="Archived" value="ARCHIVED" />
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-button type="primary" @click="fetchAnalyticsData">Query</el-button>
            <el-button @click="resetFilters">Reset</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(card, index) in overviewCards" :key="index">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">{{ card.value }}</div>
              <div class="card-trend" :class="card.trend > 0 ? 'positive' : card.trend < 0 ? 'negative' : ''">
                <span>{{ card.trend > 0 ? '+' : '' }}{{ card.trend }}%</span>
                <el-icon v-if="card.trend > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="card.trend < 0"><ArrowDown /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="chart-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Ad Set Performance Trend</span>
                <el-radio-group v-model="trendMetric" size="small">
                  <el-radio-button label="impressions">Impressions</el-radio-button>
                  <el-radio-button label="clicks">Clicks</el-radio-button>
                  <el-radio-button label="conversions">Conversions</el-radio-button>
                  <el-radio-button label="spend">Spend</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container" ref="trendChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!--<div class="chart-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Audience Distribution</span>
              </div>
            </template>
            <div class="chart-container" ref="audienceChartRef"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Platform Analysis</span>
              </div>
            </template>
            <div class="chart-container" ref="platformChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>-->

    <div class="chart-section">
      <el-card shadow="hover" class="chart-card">
        <template #header>
          <div class="chart-header">
            <span>Ad Set Performance Comparison</span>
            <el-select v-model="compareMetric" size="small" style="width: 120px">
              <el-option label="CTR" value="ctr" />
              <el-option label="Conversion Rate" value="conversionRate" />
              <el-option label="Avg. CPC" value="cpc" />
              <el-option label="CPM" value="cpm" />
            </el-select>
          </div>
        </template>
        <div class="chart-container" ref="compareChartRef"></div>
      </el-card>
    </div>

    <div class="detail-section">
      <el-card shadow="hover">
        <template #header>
          <div class="detail-header">
            <span>Ad Set Performance Details</span>
            <!--<div>
              <el-button type="primary" size="small" @click="exportData">Export Data</el-button>
            </div>-->
          </div>
        </template>
        <table-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :table-data="adsetData"
          :loading="loading"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="adset_name" label="Ad Set Name" min-width="300" />
          <el-table-column prop="campaign_name" label="Campaign" min-width="250" />
          <el-table-column prop="status" label="Status" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="impressions" label="Impressions" sortable  min-width="150"/>
          <el-table-column prop="clicks" label="Clicks" sortable  min-width="120"/>
          <el-table-column prop="ctr" label="CTR" sortable>
            <template #default="scope">
              {{ scope.row.ctr }}%
            </template>
          </el-table-column>
          <el-table-column prop="conversions" label="Conversions" sortable ortable min-width="150" />
          <el-table-column prop="conversionRate" label="Conversion Rate" sortable ortable min-width="180">
            <template #default="scope">
              {{ scope.row.conversionRate }}%
            </template>
          </el-table-column>
          <el-table-column prop="spend" label="Spend" sortable  min-width="100">
            <template #default="scope">
              ${{ scope.row.spend }}
            </template>
          </el-table-column>
          <el-table-column prop="cpc" label="Avg. CPC" sortable min-width="120">
            <template #default="scope">
              ${{ scope.row.cpc }}
            </template>
          </el-table-column>
          <el-table-column prop="cpm" label="CPM" sortable min-width="120">
            <template #default="scope">
              ${{ scope.row.cpm }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="Actions" width="220">
            <template #default="scope">
              <el-button type="text" size="small" @click.stop="viewAds(scope.row.adset_id)">
                View Ads
              </el-button>
              <el-button type="text" size="small" @click.stop="editAdset(scope.row.adset_id)">
                Edit
              </el-button>
            </template>
          </el-table-column>
        </table-pagination>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { getCampaignOptions } from '@/api/campaign'
import { getAdsetOptions } from '@/api/adset'
import { adsetinsights, overview, trends, compare } from '@/api/adsetanalytics'
import * as echarts from 'echarts'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'AdSetAnalytics',
  components: {
    TablePagination
  },
  data() {
    return {
      loading: false,
      loadingStates: {
        insights: false,
        overview: false,
        trends: false,
        compare: false
      },
      campaignOptions: [],
      adsetOptions: [],
      adsetData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      selectedAdset: null,
      filterForm: {
        dateRange: [
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
          new Date().toISOString().substr(0, 10)
        ],
        campaignId: '',
        adsetIds: [],
        status: ''
      },
      trendMetric: 'impressions',
      compareMetric: 'ctr',
      overviewCards: [
        { title: 'Total Impressions', value: '0', trend: 0 },
        { title: 'Total Clicks', value: '0', trend: 0 },
        { title: 'Total Conversions', value: '0', trend: 0 },
        { title: 'Total Spend', value: '$0.00', trend: 0 }
      ],
      trendChart: null,
      audienceChart: null,
      platformChart: null,
      compareChart: null,
      trendChartRef: null,
      //audienceChartRef: null,
      //platformChartRef: null,
      compareChartRef: null
    }
  },
  mounted() {
    this.router = useRouter()
    this.fetchCampaignOptions()
    this.fetchAnalyticsData()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
    this.disposeCharts()
  },
  watch: {
    trendMetric: {
      async handler() {
        try {
          const params = {
            beginTime: this.filterForm.dateRange[0],
            endTime: this.filterForm.dateRange[1],
            campaignId: this.filterForm.campaignId || undefined,
            adsetIds: this.filterForm.adsetIds.length > 0 ? this.filterForm.adsetIds : undefined,
            trendMetric: this.trendMetric
          }
          
          const response = await trends(params)
          if (response.code === 0) {
            this.trendsData = response.data || {}
            this.renderTrendChart(this.trendsData)
          }
        } catch (error) {
          console.error('Failed to get trend data:', error)
          ElMessage.error('Failed to get trend data')
        }
      }
    },
    compareMetric: {
      async handler() {
        try {
          const params = {
            beginTime: this.filterForm.dateRange[0],
            endTime: this.filterForm.dateRange[1],
            campaignId: this.filterForm.campaignId || undefined,
            adsetIds: this.filterForm.adsetIds.length > 0 ? this.filterForm.adsetIds : undefined,
          }
          
          const response = await compare(params)
          if (response.code === 0) {
            this.renderCompareChart(response.data || {})
          }
        } catch (error) {
          console.error('Failed to get comparison data:', error)
          ElMessage.error('Failed to get comparison data')
        }
      }
    }
  },
  methods: {
    async fetchCampaignOptions() {
      try {
        const response = await getCampaignOptions()
        if(response.code === 0 ){
          this.campaignOptions = response.data || []
        }
      } catch (error) {
        console.error('Failed to get campaign options:', error)
        ElMessage.error('Failed to get campaign options')
      }
    },
    async fetchAdsetOptions(campaignId) {
      if (!campaignId) {
        this.adsetOptions = []
        return
      }
      try {
        const response = await getAdsetOptions(campaignId)
        if(response.code === 0 ){
          this.adsetOptions = response.data || []
        }
      } catch (error) {
        console.error('Failed to get ad set options:', error)
        ElMessage.error('Failed to get ad set options')
      }
    },
    async fetchAnalyticsData() {
      this.loading = true
      try {
        const params = {
          beginTime: this.filterForm.dateRange[0],
          endTime: this.filterForm.dateRange[1],
          campaignId: this.filterForm.campaignId || undefined,
          adsetIds: this.filterForm.adsetIds.length > 0 ? this.filterForm.adsetIds : undefined,
          page: this.currentPage,
          limit: this.pageSize,
          trendMetric: this.trendMetric
        }

        // 1. Get data insights
        this.loadingStates.insights = true
        const insightsRes = await adsetinsights(params)
        if (insightsRes.code === 0) {
          this.adsetData = insightsRes.data || []
          this.total = insightsRes.total || 0
        }
        this.loadingStates.insights = false

        // 2. Get overview data
        this.loadingStates.overview = true
        const overviewRes = await overview(params)
        if (overviewRes.code === 0) {
          this.updateOverviewCards(overviewRes.data || {})
        }
        this.loadingStates.overview = false

        // 3. Get trend data
        this.loadingStates.trends = true
        const trendsRes = await trends(params)
        if (trendsRes.code === 0) {
          this.trendsData = trendsRes.data || {}
        }
        this.loadingStates.trends = false

        // 4. Get comparison data
        this.loadingStates.compare = true
        const compareRes = await compare(params)
        console.log(compareRes)
        this.loadingStates.compare = false

        // Render charts
        this.$nextTick(() => {
          this.initCharts()
          
          // Render trend chart
          if (trendsRes.code === 0) {
            this.renderTrendChart(this.trendsData)
          }
          
          // Render comparison chart
          if (compareRes.code === 0) {
            this.renderCompareChart(compareRes.data || {})
          }
        })
      } catch (error) {
        console.error('Failed to get analytics data:', error)
        ElMessage.error('Failed to get analytics data')
      } finally {
        this.loading = false
        // Reset all loading states
        Object.keys(this.loadingStates).forEach(key => {
          this.loadingStates[key] = false
        })
      }
    },
    updateOverviewCards(overview) {
      if (!overview) return
      this.overviewCards = [
        { title: 'Total Impressions', value: this.formatNumber(overview.impressions), trend: overview.impressionsTrend },
        { title: 'Total Clicks', value:  this.formatNumber(overview.clicks), trend: overview.clicksTrend },
        { title: 'Total Conversions', value: this.formatNumber(overview.conversions), trend: overview.conversionsTrend },
        { title: 'Total Spend', value: `$${this.formatNumber(overview.todaySpend)}`, trend: overview.todaySpendTrend }
      ]
    },
    disposeCharts() {
      if (this.trendChart) this.trendChart.dispose()
      //if (this.audienceChart) this.audienceChart.dispose()
      //if (this.platformChart) this.platformChart.dispose()
      if (this.compareChart) this.compareChart.dispose()
    },
    initCharts() {
      this.disposeCharts()
      this.trendChart = echarts.init(this.$refs.trendChartRef)
      //this.audienceChart = echarts.init(this.$refs.audienceChartRef)
      //this.platformChart = echarts.init(this.$refs.platformChartRef)
      this.compareChart = echarts.init(this.$refs.compareChartRef)
    },
    renderTrendChart(trends = []) {
      if (!this.trendChart) return
 
      const dates = trends?.xAxis || []
      const values = trends?.series || []
      
      const option = {
        tooltip: { trigger: 'axis' },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: { type: 'category', boundaryGap: false, data: dates },
        yAxis: { type: 'value' },
        series: [
          {
            name: this.getMetricName(this.trendMetric),
            type: 'line',
            smooth: true,
            data: values,
            itemStyle: { color: '#409EFF' },
            areaStyle: {
              color: {
                type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.7)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ]
              }
            }
          }
        ]
      }
      this.trendChart.setOption(option)
    },
    /*renderAudienceChart(data = []) {
      if (!this.audienceChart) return
      const option = {
        tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
        legend: { orient: 'vertical', right: 10, top: 'center', data: data.map(item => item.name) },
        series: [
          {
            name: 'Audience Distribution',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: { show: false, position: 'center' },
            emphasis: { label: { show: true, fontSize: '14', fontWeight: 'bold' } },
            labelLine: { show: false },
            data: data
          }
        ]
      }
      this.audienceChart.setOption(option)
    },
    renderPlatformChart(data = []) {
      if (!this.platformChart) return
      const option = {
        tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
        legend: { orient: 'vertical', right: 10, top: 'center', data: data.map(item => item.name) },
        series: [
          {
            name: 'Platform Distribution',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: { show: false, position: 'center' },
            emphasis: { label: { show: true, fontSize: '14', fontWeight: 'bold' } },
            labelLine: { show: false },
            data: data
          }
        ]
      }
      this.platformChart.setOption(option)
    },*/
    renderCompareChart(data = []) {
      if (!this.compareChart) return
      data.sort((a, b) => b[this.compareMetric] - a[this.compareMetric])
      const displayData = data//.slice(0, 10)
      const formatValue = (value) => {
        if (this.compareMetric === 'ctr' || this.compareMetric === 'conversionRate') {
          return value.toFixed(2) + '%'
        } else if (this.compareMetric === 'cpc' || this.compareMetric === 'cpm') {
          return '$' + value.toFixed(2)
        }
        return value.toFixed(2)
      }
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: function(params) {
            return `${params[0].name}: ${formatValue(params[0].value)}`
          }
        },
        grid: { left: '3%', right: '15%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'value',
          axisLabel: { formatter: function(value) { return formatValue(value) } }
        },
        yAxis: {
          type: 'category',
          data: displayData.map(item => item.name),
          axisLabel: {
            formatter: function(value) {
              if (value.length > 10) return value.substring(0, 10) + '...'
              return value
            }
          }
        },
        series: [
          {
            name: this.getCompareMetricName(this.compareMetric),
            type: 'bar',
            data: displayData.map(item => item[this.compareMetric]),
            itemStyle: {
              color: (params) => {
                const value = params.value
                const max = Math.max(...displayData.map(item => item[this.compareMetric]))
                const ratio = value / max
                if (this.compareMetric === 'cpc' || this.compareMetric === 'cpm') {
                  return ratio < 0.3 ? '#67C23A' : ratio < 0.6 ? '#E6A23C' : '#F56C6C'
                } else {
                  return ratio > 0.7 ? '#67C23A' : ratio > 0.4 ? '#E6A23C' : '#F56C6C'
                }
              }
            },
            label: {
              show: true,
              position: 'right',
              formatter: function(params) { return formatValue(params.value) }
            }
          }
        ]
      }
      this.compareChart.setOption(option)
    },
    handleResize() {
      this.trendChart && this.trendChart.resize()
      this.audienceChart && this.audienceChart.resize()
      this.platformChart && this.platformChart.resize()
      this.compareChart && this.compareChart.resize()
    },
    handleCampaignChange() {
      this.filterForm.adsetIds = []
      this.fetchAdsetOptions(this.filterForm.campaignId)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchAnalyticsData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchAnalyticsData()
    },
    handleRowClick(row) {
      this.selectedAdset = row
    },
    viewAdsetDetail(id) {
      this.$router.push({
        path: `/analytics/adset-detail/${id}`,
        query: {
          startDate: this.filterForm.dateRange[0],
          endDate: this.filterForm.dateRange[1]
        }
      })
    },
    viewAds(id) {
      this.$router.push({
        path: '/ads',
        query: { adsetId: id }
      })
    },
    editAdset(id) {
      this.$router.push({
        path: `/adsets/edit/${id}`
      })
    },
    resetFilters() {
      this.filterForm = {
        dateRange: [
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().substr(0, 10),
          new Date().toISOString().substr(0, 10)
        ],
        campaignId: '',
        adsetIds: [],
        status: ''
      }
      this.fetchAnalyticsData()
    },
    exportData() {
      ElMessage.success('Data exported')
    },
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num?.toString() || '0'
    },
    getMetricName(metric) {
      const metricNames = {
        impressions: 'Impressions',
        clicks: 'Clicks',
        conversions: 'Conversions',
        spend: 'Spend'
      }
      return metricNames[metric] || metric
    },
    getCompareMetricName(metric) {
      const metricNames = {
        ctr: 'CTR',
        conversionRate: 'Conversion Rate',
        cpc: 'Avg. CPC',
        cpm: 'CPM'
      }
      return metricNames[metric] || metric
    },
    getStatusName(status) {
      const statusNames = {
        ACTIVE: 'Active',
        PAUSED: 'Paused',
        ARCHIVED: 'Archived',
        DELETED: 'Deleted'
      }
      return statusNames[status] || status
    },
    getStatusType(status) {
      const statusTypes = {
        ACTIVE: 'success',
        PAUSED: 'warning',
        ARCHIVED: 'info',
        DELETED: 'danger'
      }
      return statusTypes[status] || ''
    }
  }
}
</script>

<style scoped>
.analytics-container {
  padding: 20px;
}

.header-section {
  margin-bottom: 20px;
}

.filter-section {
  margin-top: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.card-title {
  font-size: 14px;
  color: #606266;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin: 8px 0;
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.card-trend.positive {
  color: #67c23a;
}

.card-trend.negative {
  color: #f56c6c;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header, .detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.detail-section {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 