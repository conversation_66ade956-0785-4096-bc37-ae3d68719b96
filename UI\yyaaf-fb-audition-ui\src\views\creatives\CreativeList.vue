<template>
  <div class="creative-list-container">
    <div class="header-actions">
      <div class="title">Ad Creatives List</div>
      <div class="actions">
        <el-button type="primary" @click="$router.push('/creatives/create')">
          <el-icon><Plus /></el-icon>
          Create Creative
        </el-button>
        <el-button type="danger" :disabled="!hasSelected" @click="handleBatchDelete">Batch Delete</el-button>
      </div>
    </div>

    <!-- Creative List -->
    <el-table
      v-loading="loading"
      :data="creativeList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="Creative ID" min-width="120" />
      <el-table-column prop="name" label="Creative Name" min-width="150" show-overflow-tooltip />
      <el-table-column label="Page Info" min-width="150" align="center">
        <template #default="scope">
          <span>{{ scope.row.page_name }}</span><br/>
          <span>{{ scope.row.object_story_spec.page_id }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="Creative Type" min-width="100" align="center">
        <template #default="scope">
          <el-tag :type="getCreativeTypeTag(scope.row.type)">
            {{ getCreativeTypeName(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Creative Preview" min-width="120" align="center">
        <template #default="scope">
          <div class="creative-preview" @click="previewCreative(scope.row)">
            <el-image
              v-if="scope.row.type === 'IMAGE' || scope.row.type === 'CAROUSEL' || scope.row.type === 'DYNAMIC'"
              :src="scope.row.thumbnail_url || scope.row.image_url"
              fit="cover"
              class="preview-image"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div 
              v-else-if="scope.row.type === 'VIDEO'" 
              class="video-preview"
              :style="{ backgroundImage: `url(${scope.row.thumbnail_url})` }"
            >
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="Title" min-width="150" show-overflow-tooltip />
      <el-table-column prop="message" label="Message" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.type === 'IMAGE'" class="ellipsis-text">{{ scope.row.object_story_spec.link_data.message }}</span>
          <span v-else-if="scope.row.type === 'VIDEO'" class="ellipsis-text">{{ scope.row.object_story_spec.video_data.message }}</span>
          <span v-else class="ellipsis-text">--</span>
        </template>
      </el-table-column>
      <el-table-column label="Actions" min-width="200">
        <template #default="scope">
          <el-button 
            type="primary" 
            text
            @click="previewCreative(scope.row)"
          >
            Preview
          </el-button>
          <el-button 
            type="primary" 
            text
            @click="$router.push(`/creatives/edit/${scope.row.id}`)"
          >
            Edit
          </el-button>
          <el-button 
            type="danger" 
            text
            @click="deleteCreative(scope.row)"
          >
            Delete
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination -->
    <div class="pagination-container">
      <cursor-pagination
        :has-next="pagination.hasNext"
        :has-previous="pagination.hasPrevious"
        :after="pagination.after"
        :before="pagination.before"
        @next="loadNextPage"
        @previous="loadPreviousPage"
      />
    </div>

    <!-- Preview Dialog -->
    <el-dialog
      v-model="dialogVisible.preview"
      title="Creative Preview"
      width="800px"
    >
      <div v-if="currentCreative" class="creative-preview-container">
        <div class="creative-media">
          <el-image
            v-if="currentCreative.type === 'IMAGE'"
            :src="currentCreative.image_url"
            fit="contain"
            class="preview-full-image"
            :preview-src-list="[currentCreative.image_url]"
          />
          <el-carousel
            v-else-if="currentCreative.type === 'CAROUSEL'"
            height="400px"
            indicator-position="outside"
            arrow="always"
          >
            <el-carousel-item v-for="(item, index) in currentCreative.carouselImages" :key="index">
              <el-image :src="item.url" fit="contain" class="preview-full-image" :preview-src-list="[item.url]" />
            </el-carousel-item>
          </el-carousel>
          <video
            v-else-if="currentCreative.type === 'VIDEO'"
            :src="currentCreative.object_story_spec.video_data.video_url"
            controls
            class="preview-video"
          ></video>
        </div>
        <div class="creative-info">
          <h3>{{ currentCreative.title }}</h3>
          <p class="creative-message" v-if="currentCreative.type === 'IMAGE'"><strong>Message:</strong>{{ currentCreative.object_story_spec.link_data.message }}</p>
          <p class="creative-message" v-else-if="currentCreative.type === 'VIDEO'"><strong>Message:</strong>{{ currentCreative.object_story_spec.video_data.message }}</p>
          <p class="creative-cta">
            <strong>Call to Action:</strong>
            <el-tag effect="plain" v-if="currentCreative.type === 'IMAGE'">{{ getCallToActionName(currentCreative.object_story_spec.link_data.call_to_action.type) }}</el-tag>
            <el-tag effect="plain" v-else-if="currentCreative.type === 'VIDEO'">{{ getCallToActionName(currentCreative.object_story_spec.video_data.call_to_action.type) }}</el-tag>
          </p>
          <p class="creative-link" v-if="currentCreative.type === 'IMAGE'">
            <strong>Link:</strong>
            <a :href="currentCreative.object_story_spec.link_data.link" target="_blank">{{ currentCreative.object_story_spec.link_data.link }}</a>
          </p>
          <p class="creative-link" v-else-if="currentCreative.type === 'VIDEO'">
            <strong>Link:</strong>
            <a :href="currentCreative.object_story_spec.video_data.call_to_action.value.link" target="_blank">{{ currentCreative.object_story_spec.video_data.call_to_action.value.link }}</a>
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Plus, Picture, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCreativeList, deleteCreative } from '@/api/creative'
import CursorPagination from '@/components/pagination/CursorPagination.vue'

export default {
  name: 'CreativeList',
  components: {
    Plus,
    Picture,
    VideoPlay,
    CursorPagination
  },
  data() {
    return {
      // Status
      loading: false,
      creativeList: [],
      currentCreative: null,
      dialogVisible: {
        preview: false
      },
      
      // Pagination
      pagination: {
        after: '',
        before: '',
        hasNext: false,
        hasPrevious: false
      },
      selectedCreatives: []
    }
  },
  computed: {
    // Calculate if there are selected rows
    hasSelected() {
      return this.selectedCreatives.length > 0
    }
  },
  methods: {
     // Define refresh function
     refreshLibrary() {
      this.pagination.after = ''
      this.pagination.before = ''
      this.fetchCreativeList()
    },
    // Get creative list
    async fetchCreativeList() {
      this.loading = true
      try {
        // Build query parameters
        const params = {
          limit: 12
        }
        
        // Add Facebook API pagination parameters
        if (this.pagination.after) {
          params.after = this.pagination.after
        }
        if (this.pagination.before) {
          params.before = this.pagination.before
        }
        
        // Call API to get creative list
        const res = await getCreativeList(params)
        
        // Process creative data
        this.creativeList = res.data || []
        
        // Update pagination information
        this.pagination.after = res.paging?.cursors?.after || ''
        this.pagination.before = res.paging?.cursors?.before || ''
        this.pagination.hasNext = !!res.paging?.next
        this.pagination.hasPrevious = !!res.paging?.previous
        
      } catch (error) {
        console.error('Failed to get creative list:', error)
        ElMessage.error('Failed to get creative list')
      } finally {
        this.loading = false
      }
    },
    
    // Load previous page
    loadPreviousPage() {
      if (this.pagination.hasPrevious) {
        this.pagination.after = '' // Clear after parameter
        this.fetchCreativeList()
      }
    },
    
    // Load next page
    loadNextPage() {
      if (this.pagination.hasNext) {
        this.pagination.before = '' // Clear before parameter
        this.fetchCreativeList()
      }
    },
    
    // Preview creative
    previewCreative(creative) {
      this.currentCreative = creative
      this.dialogVisible.preview = true
    },
    
    // Delete creative
    deleteCreative(creative) {
      ElMessageBox.confirm(`Are you sure you want to delete the creative "${creative.name}"?`, 'Tip', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteCreative({id:creative.id})
          if (res.code === 0) {
            ElMessage.success('Deleted successfully') 
            this.refreshLibrary()
          } else {
            ElMessage.error('Failed to delete')
          }
        } catch (error) {
          console.error('Failed to delete creative:', error)
          ElMessage.error('Failed to delete creative')
        }
      }).catch(() => {})
    },
    
    // Get creative type name
    getCreativeTypeName(type) {
      const types = {
        'IMAGE': 'Image Creative',
        'VIDEO': 'Video Creative',
        'CAROUSEL': 'Carousel Creative'
      }
      return types[type] || type
    },
    
    // Get creative type tag style
    getCreativeTypeTag(type) {
      const tags = {
        'IMAGE': '',
        'VIDEO': 'success',
        'CAROUSEL': 'warning'
      }
      return tags[type] || ''
    },
    
    // Get call to action button name
    getCallToActionName(cta) {
      const ctaNames = {
        'OPEN_LINK': 'Open Link',
        'SHOP_NOW': 'Shop Now',
        'LEARN_MORE': 'Learn More',
        'SIGN_UP': 'Sign Up',
        'DOWNLOAD': 'Download',
        'VIEW_MORE': 'View More',
        'CONTACT_US': 'Contact Us',
        'APPLY_NOW': 'Apply Now',
        'BOOK_NOW': 'Book Now',
        'GET_OFFER': 'Get Offer',
        'TRY_IT': 'Try It'
      }
      return ctaNames[cta] || cta
    },
    
    handleSelectionChange(selection) {
      this.selectedCreatives = selection
    },
    
    handleBatchDelete() {
      if (this.selectedCreatives.length === 0) {
        return ElMessage.warning('Please select creatives to delete first')
      }

      ElMessageBox.confirm('Are you sure you want to batch delete the selected creatives?', 'Confirm Delete', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(async () => {
        const ids = this.selectedCreatives.map(item => item.id)
        
        try {
          // TODO: Implement batch delete API
          const res = await Promise.all(ids.map(id => deleteCreative({id:id})))
          // Check if all delete operations are successful
          const allSuccess = res.every(item => item.code === 0)
          if (allSuccess) {
            ElMessage.success('Batch delete successful')
          } else {
            // Partial success partial failure
            const successCount = res.filter(item => item.code === 0).length
            ElMessage.warning(`Delete operation partially successful, succeeded ${successCount}/${res.length} items`)
          }
          this.refreshLibrary()
        } catch (error) {
          console.error('Failed to batch delete creatives:', error)
          ElMessage.error('Failed to batch delete creatives')
        }
      }).catch(() => {
        // Cancel delete, do nothing
      })
    }
  },
  mounted() {
    this.fetchCreativeList()
  }
}
</script>

<style scoped>
.creative-list-container {
  padding: 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions .title {
  font-size: 20px;
  font-weight: bold;
}

.header-actions .actions {
  display: flex;
  gap: 10px;
}

.creative-preview {
  width: 60px;
  height: 60px;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #909399;
  font-size: 20px;
}

.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  position: relative;
}

.video-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}

.play-icon {
  color: white;
  font-size: 24px;
  z-index: 1;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.creative-preview-container {
  display: flex;
  flex-direction: column;
}

.creative-media {
  width: 100%;
  margin-bottom: 20px;
}

.preview-full-image {
  max-width: 100%;
  max-height: 400px;
}

.preview-video {
  width: 100%;
  max-height: 400px;
}

.creative-info {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.creative-message {
  margin: 10px 0;
  color: #606266;
}

.creative-cta {
  margin: 10px 0;
}

.creative-link {
  margin: 10px 0;
  word-break: break-all;
}

.creative-link a {
  color: #409eff;
  text-decoration: none;
}

.creative-link a:hover {
  text-decoration: underline;
}
</style> 