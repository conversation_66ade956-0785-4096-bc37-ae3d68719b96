<template>
  <div class="analytics-container">
    <div class="header-section">
      <h2>Campaign Analytics</h2>
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="Date Range">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="To"
              start-placeholder="Start Date"
              end-placeholder="End Date"
              value-format="YYYY-MM-DD"
              @change="fetchAnalyticsData"
            />
          </el-form-item>
          <el-form-item label="Campaign">
            <el-select
              v-model="filterForm.campaignIds"
              multiple
              collapse-tags
              placeholder="Select Campaign"
              filterable
              clearable
            >
              <el-option
                v-for="item in campaignOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!--<el-form-item label="Objective">
            <el-select
              v-model="filterForm.objective"
              placeholder="Select Objective"
              clearable
              style="width: 200px;"
            >
              <el-option label="Brand Awareness" value="BRAND_AWARENESS" />
              <el-option label="Traffic" value="TRAFFIC" />
              <el-option label="App Installs" value="APP_INSTALLS" />
              <el-option label="Video Views" value="VIDEO_VIEWS" />
              <el-option label="Conversions" value="CONVERSIONS" />
            </el-select>
          </el-form-item>
          <el-form-item label="Status">
            <el-select
              v-model="filterForm.status"
              placeholder="Select Status"
              clearable
              style="width: 200px;"
            >
              <el-option label="Active" value="ACTIVE" />
              <el-option label="Paused" value="PAUSED" />
              <el-option label="Archived" value="ARCHIVED" />
            </el-select>
          </el-form-item>-->
          <el-form-item>
            <el-button type="primary" @click="fetchAnalyticsData">Query</el-button>
            <el-button @click="resetFilters">Reset</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(card, index) in overviewCards" :key="index">
          <el-card shadow="hover" class="overview-card">
            <div class="card-content">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">{{ card.value }}</div>
              <div class="card-trend" :class="card.trend > 0 ? 'positive' : card.trend < 0 ? 'negative' : ''">
                <span>{{ card.trend > 0 ? '+' : '' }}{{ card.trend }}%</span>
                <el-icon v-if="card.trend > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="card.trend < 0"><ArrowDown /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="chart-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Campaign Performance Trend</span>
                <el-radio-group v-model="trendMetric" size="small">
                  <el-radio-button label="impressions">Impressions</el-radio-button>
                  <el-radio-button label="clicks">Clicks</el-radio-button>
                  <el-radio-button label="conversions">Conversions</el-radio-button>
                  <el-radio-button label="spend">Spend</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container" ref="trendChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="chart-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Distribution by Objective</span>
              </div>
            </template>
            <div class="chart-container" ref="objectiveChartRef"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>Distribution by Status</span>
              </div>
            </template>
            <div class="chart-container" ref="statusChartRef"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!--<div class="benchmark-section">
      <el-card shadow="hover">
        <template #header>
          <div class="benchmark-header">
            <span>Industry Benchmark Comparison</span>
            <el-select v-model="benchmarkMetric" size="small" style="width: 120px">
              <el-option label="CTR" value="ctr" />
              <el-option label="Conversion Rate" value="conversionRate" />
              <el-option label="CPM" value="cpm" />
              <el-option label="ROAS" value="roas" />
            </el-select>
          </div>
        </template>
        <div class="chart-container" ref="benchmarkChartRef"></div>
      </el-card>
    </div>-->

    <div class="detail-section">
      <el-card shadow="hover">
        <template #header>
          <div class="detail-header">
            <span>Campaign Performance Details</span>
            <div>
              <!--<el-button type="primary" size="small" @click="exportData">Export Data</el-button>-->
            </div>
          </div>
        </template>
        <table-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :table-data="campaignData"
          :loading="loading"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="campaign_name" label="Campaign Name" min-width="250" />
          <el-table-column prop="objective" label="Objective" min-width="180">
            <template #default="scope">
              {{ getObjectiveName(scope.row.objective) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="Status" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="impressions" label="Impressions" sortable  min-width="150"/>
          <el-table-column prop="clicks" label="Clicks" sortable  min-width="120"/>
          <el-table-column prop="ctr" label="CTR" sortable>
            <template #default="scope">
              {{ (scope.row.ctr) }}%
            </template>
          </el-table-column>
          <el-table-column prop="conversions" label="Conversions" sortable  min-width="150"/>
          <el-table-column prop="conversionRate" label="Conversion Rate" sortable  min-width="180">
            <template #default="scope">
              {{ (scope.row.conversionRate) }}%
            </template>
          </el-table-column>
          <el-table-column prop="spend" label="Spend" sortable  min-width="100">
            <template #default="scope">
              ${{ scope.row.spend }}
            </template>
          </el-table-column>
          <el-table-column prop="cpc" label="Avg. CPC" sortable min-width="120">
            <template #default="scope">
              ${{ scope.row.cpc }}
            </template>
          </el-table-column>
          <el-table-column prop="cpm" label="CPM" sortable min-width="120">
            <template #default="scope">
              ${{ scope.row.cpm }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="Actions" width="220">
            <template #default="scope">
              <el-button type="text" size="small" @click.stop="viewAdsets(scope.row.campaign_id)">
                View Ad Sets
              </el-button>
              <el-button type="text" size="small" @click.stop="editCampaign(scope.row.campaign_id)">
                Edit
              </el-button>
            </template>
          </el-table-column>
        </table-pagination>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getCampaignOptions } from '@/api/campaign'
import { campaigninsights, overview, trends, objective, status } from '@/api/campaignanalytics'
import * as echarts from 'echarts'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'CampaignAnalytics',
  components: {
    ArrowUp,
    ArrowDown,
    TablePagination
  },
  data() {
    return {
      // Chart instances
      trendChart: null,
      objectiveChart: null,
      statusChart: null,
      benchmarkChart: null,
      
      // Store trend data
      trendsData: null,
      
      // Status variables
      loading: false,
      campaignOptions: [],
      campaignData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      selectedCampaign: null,
      filterForm: {
        dateRange: [
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          new Date().toISOString().split('T')[0]
        ],
        campaignIds: [],
        objective: '',
        status: ''
      },
      trendMetric: 'impressions',
      benchmarkMetric: 'ctr',
      overviewCards: [
        { title: 'Total Impressions', value: '0', trend: 0 },
        { title: 'Total Clicks', value: '0', trend: 0 },
        { title: 'Total Conversions', value: '0', trend: 0 },
        { title: 'Total Spend', value: '$0.00', trend: 0 }
      ],
      
      // Loading status for each module
      loadingStates: {
        insights: false,
        overview: false,
        trends: false,
        objective: false,
        status: false
      }
    }
  },
  watch: {
    trendMetric: {
      async handler() {
        try {
          const params = {
            beginTime: this.filterForm.dateRange[0],
            endTime: this.filterForm.dateRange[1],
            trendMetric: this.trendMetric
          }
          
          const response = await trends(params)
          if (response.code === 0) {
            this.trendsData = response.data || {}
            this.renderTrendChart(this.trendsData)
          }
        } catch (error) {
          console.error('Failed to get trend data:', error)
          ElMessage.error('Failed to get trend data')
        }
      }
    },
    benchmarkMetric: {
      handler() {
        this.renderBenchmarkChart()
      }
    }
  },
  mounted() {
    this.fetchCampaignOptions()
    this.fetchAnalyticsData()
    
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
    
    // Destroy chart instances
    if (this.trendChart) this.trendChart.dispose()
    if (this.objectiveChart) this.objectiveChart.dispose()
    if (this.statusChart) this.statusChart.dispose()
    if (this.benchmarkChart) this.benchmarkChart.dispose()
  },
  methods: {
    // Get campaign options
    async fetchCampaignOptions() {
      try {
        const response = await getCampaignOptions()

        if(response.code ===0 ){
          this.campaignOptions = response.data || []
        }

      } catch (error) {
        console.error('Failed to get campaign options:', error)
        ElMessage.error('Failed to get campaign options')
      }
    },
    // Get analytics data
    async fetchAnalyticsData() {
      this.loading = true
      try {
        const params = {
          beginTime: this.filterForm.dateRange[0],
          endTime: this.filterForm.dateRange[1],
          campaignIds: this.filterForm.campaignIds.length > 0 ? this.filterForm.campaignIds : undefined,
          page: this.currentPage,
          limit: this.pageSize,
          trendMetric: this.trendMetric
        }
        
        // Request data separately
        // 1. Get data insights
        this.loadingStates.insights = true
        const insightsRes = await campaigninsights(params)
        if (insightsRes.code === 0) {
          this.campaignData = insightsRes.data || []
          this.total = insightsRes.total || 0
        }
        this.loadingStates.insights = false
        
        // 2. Get overview data
        this.loadingStates.overview = true
        const overviewRes = await overview(params)
        if (overviewRes.code === 0) {
          this.updateOverviewCards(overviewRes.data || {})
        }
        this.loadingStates.overview = false
        
        // 3. Get trend data
        this.loadingStates.trends = true
        const trendsRes = await trends(params)
        if (trendsRes.code === 0) {
          this.trendsData = trendsRes.data || {}
        }
        this.loadingStates.trends = false
        
        // 4. Get objective distribution data
        this.loadingStates.objective = true
        const objectiveRes = await objective(params)
        this.loadingStates.objective = false
        
        // 5. Get status distribution data
        this.loadingStates.status = true
        const statusRes = await status(params)
        this.loadingStates.status = false
        
        // Render charts
        this.$nextTick(() => {
          this.initCharts()
          
          // Render trend chart
          if (trendsRes.code === 0) {
            this.renderTrendChart(this.trendsData)
          }
          
          // Render objective distribution chart
          if (objectiveRes.code === 0) {
            this.renderObjectiveChart(objectiveRes.data || {})
          }
          
          // Render status distribution chart
          if (statusRes.code === 0) {
            this.renderStatusChart(statusRes.data || {})
          }
        })
        
      } catch (error) {
        console.error('Failed to get analytics data:', error)
        ElMessage.error('Failed to get analytics data')
      } finally {
        this.loading = false
        // Reset all loading states
        Object.keys(this.loadingStates).forEach(key => {
          this.loadingStates[key] = false
        })
      }
    },
    // Update overview card data
    updateOverviewCards(overview) {
      if (!overview) return
     
      this.overviewCards = [
        { 
          title: 'Total Impressions', 
          value: this.formatNumber(overview.impressions), 
          trend: overview.impressionsTrend 
        },
        { 
          title: 'Total Clicks', 
          value: this.formatNumber(overview.clicks), 
          trend: overview.clicksTrend 
        },
        { 
          title: 'Total Conversions', 
          value: this.formatNumber(overview.conversions), 
          trend: overview.conversionsTrend 
        },
        { 
          title: 'Total Spend', 
          value: `$${this.formatNumber(overview.todaySpend)}`, 
          trend: overview.todaySpendTrend 
        }
      ]
    },
    // Initialize charts
    initCharts() {
      // Destroy existing chart instances
      if (this.trendChart) this.trendChart.dispose()
      if (this.objectiveChart) this.objectiveChart.dispose()
      if (this.statusChart) this.statusChart.dispose()
      if (this.benchmarkChart) this.benchmarkChart.dispose()
      
      // Create new chart instances
      this.trendChart = echarts.init(this.$refs.trendChartRef)
      this.objectiveChart = echarts.init(this.$refs.objectiveChartRef)
      this.statusChart = echarts.init(this.$refs.statusChartRef)
      //this.benchmarkChart = echarts.init(this.$refs.benchmarkChartRef)
    },
    // Render trend chart
    renderTrendChart(trends) {
      if (!this.trendChart) return
      
      const dates = trends?.xAxis || []
      const values = trends?.series || []
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: this.getMetricName(this.trendMetric),
            type: 'line',
            smooth: true,
            data: values,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.7)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0.1)'
                  }
                ]
              }
            }
          }
        ]
      }
      
      this.trendChart.setOption(option)
    },
    // Render objective distribution chart
    renderObjectiveChart(data = []) {
      if (!this.objectiveChart) return
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data?.legendData || []
        },
        series: [
          {
            name: 'Ad Objective',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data?.seriesData || []
          }
        ]
      }
      
      this.objectiveChart.setOption(option)
    },
    // Render status distribution chart
    renderStatusChart(data = {}) {
      if (!this.statusChart) return
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data?.legendData || []
        },
        series: [
          {
            name: 'Campaign Status',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data:  data?.seriesData || []
          }
        ]
      }
      
      this.statusChart.setOption(option)
    },
    // Render ROI analysis chart
    renderBenchmarkChart(data = []) {
      if (!this.benchmarkChart) return
      
      // Sort by ROI value
      data.sort((a, b) => b.roi - a.roi)
      
      // Display top 10 at most
      const displayData = data.slice(0, 10)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '15%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: displayData.map(item => item.name),
          axisLabel: {
            formatter: function(value) {
              if (value.length > 12) {
                return value.substring(0, 12) + '...'
              }
              return value
            }
          }
        },
        series: [
          {
            name: 'ROI',
            type: 'bar',
            data: displayData.map(item => item.roi),
            itemStyle: {
              color: function(params) {
                // Set color based on ROI value
                const value = params.value;
                if (value > 5) return '#67C23A';
                if (value > 3) return '#E6A23C';
                if (value > 1) return '#909399';
                return '#F56C6C';
              }
            },
            label: {
              show: true,
              position: 'right'
            }
          }
        ]
      }
      
      this.benchmarkChart.setOption(option)
    },
    // Handle window resize
    handleResize() {
      this.trendChart && this.trendChart.resize()
      this.objectiveChart && this.objectiveChart.resize()
      this.statusChart && this.statusChart.resize()
      this.benchmarkChart && this.benchmarkChart.resize()
    },
    // Handle current page change
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchAnalyticsData()
    },
    // Handle selection change
    handleSelectionChange(selection) {
      console.log('Selected rows:', selection)
    },
    // Row click handler
    handleRowClick(row) {
      this.selectedCampaign = row
    },
    // View campaign details
    viewCampaignDetail(id) {
      this.$router.push({
        path: `/analytics/campaign-detail/${id}`,
        query: {
          beginTime: this.filterForm.dateRange[0],
          endTime: this.filterForm.dateRange[1]
        }
      })
    },
    // View ad sets
    viewAdsets(id) {
      this.$router.push({
        path: '/adsets',
        query: {
          campaignId: id
        }
      })
    },
    // Edit campaign
    editCampaign(id) {
      this.$router.push({
        path: `/campaigns/edit/${id}`
      })
    },
    // Reset filters
    resetFilters() {
      this.filterForm = {
        dateRange: [
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          new Date().toISOString().split('T')[0]
        ],
        campaignIds: [],
        objective: '',
        status: ''
      }
      this.fetchAnalyticsData()
    },
    // Export data
    exportData() {
      ElMessage.success('Data exported')
    },
    // Helper function - Format number
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },
    // Helper function - Get metric name
    getMetricName(metric) {
      const metricNames = {
        impressions: 'Impressions',
        clicks: 'Clicks',
        conversions: 'Conversions',
        spend: 'Spend'
      }
      return metricNames[metric] || metric
    },
    // Helper function - Get objective name
    getObjectiveName(objective) {
      const objectiveNames = {
        BRAND_AWARENESS: 'Brand Awareness',
        TRAFFIC: 'Traffic',
        APP_INSTALLS: 'App Installs',
        VIDEO_VIEWS: 'Video Views',
        CONVERSIONS: 'Conversions'
      }
      return objectiveNames[objective] || objective
    },
    // Helper function - Get status name
    getStatusName(status) {
      const statusNames = {
        ACTIVE: 'Active',
        PAUSED: 'Paused',
        ARCHIVED: 'Archived'
      }
      return statusNames[status] || status
    },
    // Helper function - Get status type
    getStatusType(status) {
      const statusTypes = {
        ACTIVE: 'success',
        PAUSED: 'warning',
        ARCHIVED: 'info'
      }
      return statusTypes[status] || ''
    }
  }
}
</script>

<style scoped>
.analytics-container {
  padding: 20px;
}

.header-section {
  margin-bottom: 20px;
}

.filter-section {
  margin-top: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.card-title {
  font-size: 14px;
  color: #606266;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin: 8px 0;
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.card-trend.positive {
  color: #67c23a;
}

.card-trend.negative {
  color: #f56c6c;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header, .detail-header, .benchmark-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.detail-section, .benchmark-section {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 