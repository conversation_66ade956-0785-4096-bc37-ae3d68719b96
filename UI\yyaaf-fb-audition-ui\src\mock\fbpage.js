import Mock from 'mockjs'

// 公共主页分类列表
const categories = ['Business', 'Entertainment', 'Brand', 'Media', 'Other']

// 生成模拟公共主页数据
function generateFBPages(userId, count = 10) {
  const pages = []
  
  for (let i = 1; i <= count; i++) {
    const pageId = `page_${userId}_${i.toString().padStart(3, '0')}`
    const category = categories[Math.floor(Math.random() * categories.length)]
    
    // 根据分类生成不同类型的名称
    let pageName
    switch (category) {
      case 'Business':
        pageName = Mock.Random.pick([
          '星空科技有限公司', '创新商贸企业', '优质服务中心', '专业咨询公司',
          '精品制造厂', '智能科技集团', '绿色环保企业', '现代物流公司'
        ])
        break
      case 'Entertainment':
        pageName = Mock.Random.pick([
          '娱乐天地', '音乐工厂', '影视制作室', '游戏乐园',
          '文化艺术中心', '演出经纪公司', '娱乐传媒', '创意工作室'
        ])
        break
      case 'Brand':
        pageName = Mock.Random.pick([
          '时尚品牌', '经典品牌', '潮流服饰', '精品生活',
          '高端定制', '品质之选', '优雅生活', '品牌故事'
        ])
        break
      case 'Media':
        pageName = Mock.Random.pick([
          '新闻资讯', '媒体中心', '传播机构', '信息平台',
          '内容创作', '数字媒体', '广播电视', '网络传媒'
        ])
        break
      default:
        pageName = Mock.Random.pick([
          '生活服务', '社区平台', '兴趣小组', '公益组织',
          '学习交流', '技术分享', '生活百科', '实用工具'
        ])
    }
    
    // 添加随机数字后缀使名称更唯一
    pageName = `${pageName}${Math.floor(Math.random() * 1000)}`
    
    const createTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    const updateTime = Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    
    pages.push({
      id: pageId,
      name: pageName,
      category: category,
      createOn: createTime,
      lastUpdateTime: Math.random() > 0.3 ? updateTime : createTime, // 30%的概率更新时间等于创建时间
      userId: userId,
      // 额外字段用于头像生成和详情显示
      avatar: null, // 将由前端生成
      description: `这是一个${category}类型的Facebook公共主页`,
      followerCount: Mock.Random.integer(100, 50000),
      isVerified: Math.random() > 0.7 // 30%的概率是认证主页
    })
  }
  
  return pages
}

// 为每个用户生成公共主页数据
const allPages = []

// 为50个用户各生成5-15个公共主页
for (let userId = 1; userId <= 50; userId++) {
  const userIdStr = `user_${userId.toString().padStart(3, '0')}`
  const pageCount = Mock.Random.integer(5, 15) // 每个用户5-15个公共主页
  const userPages = generateFBPages(userIdStr, pageCount)
  allPages.push(...userPages)
}

console.log(`Generated ${allPages.length} FB pages for 50 users`)

// Mock API 接口
Mock.mock(/\/fbpage/, 'get', (options) => {
  const url = new URL('http://localhost' + options.url)
  const params = Object.fromEntries(url.searchParams.entries())
  
  console.log('FB Page API called with params:', params)
  
  const userId = params.userId
  const page = parseInt(params.page) || 1
  const limit = parseInt(params.limit) || 20
  const name = params.name || ''
  const category = params.category || ''
  
  if (!userId) {
    return {
      code: 1,
      message: '缺少用户ID参数',
      data: null
    }
  }
  
  // 过滤用户的公共主页
  let userPages = allPages.filter(p => p.userId === userId)
  
  // 应用搜索过滤
  if (name) {
    userPages = userPages.filter(p => 
      p.name.toLowerCase().includes(name.toLowerCase())
    )
  }
  
  if (category) {
    userPages = userPages.filter(p => p.category === category)
  }
  
  // 分页
  const total = userPages.length
  const start = (page - 1) * limit
  const end = start + limit
  const pageData = userPages.slice(start, end)
  
  return {
    code: 0,
    message: 'success',
    data: pageData,
    total: total,
    page: page,
    limit: limit
  }
})

// Mock 公共主页详情API
Mock.mock(/\/fbpage\/(.+)/, 'get', (options) => {
  const pageId = options.url.split('/').pop()
  console.log('FB Page Detail API called for pageId:', pageId)
  
  const page = allPages.find(p => p.id === pageId)
  
  if (!page) {
    return {
      code: 1,
      message: '公共主页不存在',
      data: null
    }
  }
  
  // 返回详细信息
  return {
    code: 0,
    message: 'success',
    data: {
      ...page,
      // 额外的详情信息
      likes: Mock.Random.integer(500, 100000),
      about: `关于${page.name}的详细描述信息...`,
      website: `https://www.${page.name.toLowerCase().replace(/\s+/g, '')}.com`,
      phone: Mock.Random.pick(['', '+86 138-0013-8000', '+86 159-9999-6666']),
      address: Mock.Random.county(true),
      posts: Mock.Random.integer(10, 1000),
      checkins: Mock.Random.integer(0, 5000)
    }
  }
})

export default {
  allPages,
  generateFBPages
} 