<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-logo">
        <h2>FB Ad Management Backend</h2>
      </div>
      
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="off">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="Username"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            placeholder="Password"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="loading" 
            class="login-button" 
            @click="handleLogin"
          >
            Login
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { validateUsername, validatePassword } from '@/utils/validate'
import { encryptRSA} from '@/utils/encrypt'

export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: 'Please enter username', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (!validateUsername(value)) {
              callback(new Error('Username cannot be less than 3 characters'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        password: [
          { required: true, message: 'Please enter password', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (!validatePassword(value)) {
              callback(new Error('Password cannot be less than 6 characters'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleLogin() {
      this.$refs.loginFormRef.validate(valid => {
        if (valid) {
          this.loading = true
          
          let data = Object.assign({}, this.loginForm)
          data.password = encryptRSA(data.password)

          // Call the login action in the store
          this.$store.dispatch('login', {
            username: this.loginForm.username,
            password: data.password
          }).then(() => {
            this.$router.push('/')
            ElMessage.success('Login successful')
          }).catch(error => {
            ElMessage.error(error.message || 'Login failed')
          }).finally(() => {
            this.loading = false
          })
        } else {
          ElMessage.error('Please fill in the login information correctly')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  background-color: #2d3a4b;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .login-box {
    width: 400px;
    padding: 30px;
    margin: 0 auto;
    background-color: rgba(255, 255, 255, 0.04);
    border-radius: 6px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    
    .login-logo {
      text-align: center;
      margin-bottom: 30px;
      
      h2 {
        color: #fff;
        font-size: 24px;
        font-weight: bold;
      }
    }
    
    .login-form {
      .login-button {
        width: 100%;
      }
    }
  }
}

// Override Element Plus input box style
:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-input__inner) {
  color: #fff;
}

:deep(.el-input__prefix-inner) {
  color: #fff;
}
</style> 