<template>
  <div class="ad-preview-container">
    <div class="ad-preview-header">
      <span class="ad-platform"><i class="el-icon-facebook"></i> Facebook</span>
      <span class="ad-placement">{{ placementText }}</span>
    </div>
    
    <div class="ad-preview-content" :class="{ 'is-carousel': type === 'CAROUSEL' }">
      <!-- 图片广告 -->
      <div v-if="type === 'IMAGE'" class="ad-image-container">
        <el-image
          :src="imageUrl"
          fit="cover"
          class="ad-image"
        >
          <template #error>
            <div class="image-placeholder">
              <el-icon><Picture /></el-icon>
              <span>Image failed to load</span>
            </div>
          </template>
        </el-image>
      </div>
      
      <!-- 视频广告 -->
      <div v-else-if="type === 'VIDEO'" class="ad-video-container">
        <video
          v-if="videoUrl"
          class="ad-video"
          controls
          :src="videoUrl"
          :poster="videoPoster"
        ></video>
        <div v-else class="video-placeholder">
          <el-icon><VideoPlay /></el-icon>
          <span>Video not uploaded</span>
        </div>
      </div>
      
      <!-- 轮播广告 -->
      <div v-else-if="type === 'CAROUSEL'" class="ad-carousel-container">
        <el-carousel 
          :interval="4000" 
          type="card" 
          height="200px"
          :autoplay="isAutoplay"
          indicator-position="outside"
          arrow="always"
          @change="handleCarouselChange"
        >
          <el-carousel-item 
            v-for="(item, index) in carouselItems" 
            :key="index"
          >
            <el-image
              v-if="item.type === 'IMAGE'"
              :src="item.url"
              fit="cover"
              class="carousel-item-media"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            
            <video
              v-else-if="item.type === 'VIDEO'"
              class="carousel-item-media"
              :src="item.url"
              :controls="activeCarouselIndex === index"
              :poster="item.poster"
            ></video>
          </el-carousel-item>
        </el-carousel>
      </div>
      
      <!-- 预览未设置 -->
      <div v-else class="no-preview">
        <el-icon><InfoFilled /></el-icon>
        <span>Ad creative not set</span>
      </div>
    </div>
    
    <div class="ad-preview-info">
      <div class="ad-account-info">
        <span class="account-name">{{ pageInfo.name || 'Page Name' }}</span>
        <span class="sponsored">Sponsored</span>
      </div>
      
      <div class="ad-headline">{{ headline || 'Your ad headline will be displayed here' }}</div>
      
      <div class="ad-description">{{ description || 'Your ad description copy will be displayed here, detailing your product or service...' }}</div>
      
      <div class="ad-destination">
        <span class="destination-domain">{{ destinationDomain }}</span>
        <div class="cta-button" v-if="callToAction">{{ callToAction }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 广告类型: IMAGE, VIDEO, CAROUSEL
  type: {
    type: String,
    default: 'IMAGE'
  },
  // 投放位置: FEED, STORIES, RIGHT_COLUMN
  placement: {
    type: String,
    default: 'FEED'
  },
  // 单图广告图片URL
  imageUrl: {
    type: String,
    default: ''
  },
  // 视频广告URL
  videoUrl: {
    type: String,
    default: ''
  },
  // 视频封面图
  videoPoster: {
    type: String,
    default: ''
  },
  // 轮播广告项目
  carouselItems: {
    type: Array,
    default: () => []
  },
  // 广告标题
  headline: {
    type: String,
    default: ''
  },
  // 广告描述
  description: {
    type: String,
    default: ''
  },
  // 行动号召按钮文本
  callToAction: {
    type: String,
    default: 'Learn More'
  },
  // 目标URL
  destinationUrl: {
    type: String,
    default: ''
  },
  // 页面信息
  pageInfo: {
    type: Object,
    default: () => ({
      name: 'Facebook Page',
      avatar: ''
    })
  },
  // 自动播放
  autoplay: {
    type: Boolean,
    default: true
  }
})

// 获取投放位置文本
const placementText = computed(() => {
  const placementMap = {
    'FEED': 'Feed',
    'STORIES': 'Stories',
    'RIGHT_COLUMN': 'Right Column'
  }
  return placementMap[props.placement] || 'Feed'
})

// 从目标URL提取域名
const destinationDomain = computed(() => {
  if (!props.destinationUrl) return 'your-website.com'
  
  try {
    const url = new URL(props.destinationUrl)
    return url.hostname.replace(/^www\./, '')
  } catch (e) {
    // 如果不是有效URL，直接返回
    return props.destinationUrl.replace(/^https?:\/\/(www\.)?/, '').split('/')[0]
  }
})

// 轮播广告当前索引
const activeCarouselIndex = ref(0)
const isAutoplay = ref(props.autoplay)

// 处理轮播切换
const handleCarouselChange = (index) => {
  activeCarouselIndex.value = index
}

// 预览更新事件
const emit = defineEmits(['update:preview'])

// 当预览相关属性变更时，通知父组件
watch(
  [
    () => props.type,
    () => props.imageUrl,
    () => props.videoUrl,
    () => props.headline,
    () => props.description,
    () => props.carouselItems
  ],
  () => {
    emit('update:preview', {
      type: props.type,
      valid: checkPreviewValid()
    })
  },
  { deep: true }
)

// 检查预览是否有效
const checkPreviewValid = () => {
  if (props.type === 'IMAGE' && !props.imageUrl) return false
  if (props.type === 'VIDEO' && !props.videoUrl) return false
  if (props.type === 'CAROUSEL' && (!props.carouselItems || props.carouselItems.length === 0)) return false
  
  return true
}
</script>

<style lang="scss" scoped>
.ad-preview-container {
  width: 100%;
  max-width: 500px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.ad-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  
  .ad-platform {
    display: flex;
    align-items: center;
    color: #1877f2;
    font-weight: 600;
    
    i {
      margin-right: 4px;
    }
  }
  
  .ad-placement {
    font-size: 12px;
    color: #606266;
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 4px;
  }
}

.ad-preview-content {
  width: 100%;
  position: relative;
  background-color: #f5f7fa;
  
  &.is-carousel {
    padding: 12px 0;
  }
  
  .ad-image-container, .ad-video-container {
    width: 100%;
    height: 250px;
    overflow: hidden;
  }
  
  .ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .ad-video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #000;
  }
  
  .image-placeholder, .video-placeholder, .no-preview {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    color: #909399;
    
    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
  }
  
  .ad-carousel-container {
    width: 100%;
    
    :deep(.el-carousel__item) {
      border-radius: 4px;
      overflow: hidden;
    }
    
    .carousel-item-media {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.ad-preview-info {
  padding: 16px;
  
  .ad-account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .account-name {
      font-weight: 600;
      color: #1c1e21;
    }
    
    .sponsored {
      font-size: 12px;
      color: #65676b;
    }
  }
  
  .ad-headline {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 8px;
    color: #1c1e21;
  }
  
  .ad-description {
    font-size: 14px;
    line-height: 1.4;
    color: #65676b;
    margin-bottom: 12px;
  }
  
  .ad-destination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .destination-domain {
      font-size: 12px;
      color: #65676b;
      text-transform: lowercase;
    }
    
    .cta-button {
      background-color: #1877f2;
      color: white;
      font-weight: 600;
      font-size: 14px;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background-color: #166fe5;
      }
    }
  }
}
</style> 