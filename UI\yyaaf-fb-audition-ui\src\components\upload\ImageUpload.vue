<template>
  <div class="image-upload-container">
    <el-upload
      v-model:file-list="fileList"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-exceed="handleExceed"
      :limit="limit"
      :headers="headers"
      :multiple="multiple"
      :disabled="disabled"
      list-type="picture-card"
      :class="{ 'hide-upload': fileList.length >= limit && !multiple }"
    >
      <el-icon class="upload-icon"><Plus /></el-icon>
      <div class="upload-text">Click to upload</div>
      <template #tip>
        <div class="upload-tip">
          {{ tipText }}
        </div>
      </template>
    </el-upload>
    
    <!-- Image preview dialog -->
    <el-dialog v-model="previewVisible" append-to-body>
      <img class="preview-image" :src="previewUrl" alt="Preview image">
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // Upload URL
  action: {
    type: String,
    default: '/api/upload/image'
  },
  // List of uploaded files
  modelValue: {
    type: Array,
    default: () => []
  },
  // Limit on the number of uploads
  limit: {
    type: Number,
    default: 5
  },
  // Whether multiple selection is supported
  multiple: {
    type: Boolean,
    default: true
  },
  // Whether to disable
  disabled: {
    type: Boolean,
    default: false
  },
  // Tip text
  tipText: {
    type: String,
    default: 'Supports jpg/png format, single image does not exceed 5MB'
  },
  // Request headers
  customHeaders: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success', 'error', 'remove', 'exceed'])

// Upload URL
const uploadUrl = computed(() => props.action)

// Request header information
const headers = computed(() => {
  return {
    ...props.customHeaders,
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
})

// File list
const fileList = ref([])

// Preview related status
const previewVisible = ref(false)
const previewUrl = ref('')

// 同步外部传入的文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length >= 0) {
      fileList.value = [...newVal]
    }
  },
  { immediate: true, deep: true }
)

// Validate before upload
const beforeUpload = (file) => {
  // Check file type
  const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isImage) {
    ElMessage.error('Only JPG/PNG format images can be uploaded!')
    return false
  }
  
  // Check file size
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('Image size cannot exceed 5MB!')
    return false
  }
  
  return true
}

// Handle successful upload
const handleSuccess = (response, uploadFile, uploadFiles) => {
  ElMessage.success('Image uploaded successfully')
  emit('update:modelValue', uploadFiles)
  emit('success', response, uploadFile, uploadFiles)
}

// Handle upload failure
const handleError = (error, uploadFile, uploadFiles) => {
  ElMessage.error('Image upload failed, please try again')
  emit('error', error, uploadFile, uploadFiles)
}

// Remove image
const handleRemove = (uploadFile, uploadFiles) => {
  emit('update:modelValue', uploadFiles)
  emit('remove', uploadFile, uploadFiles)
}

// Preview image
const handlePreview = (uploadFile) => {
  previewUrl.value = uploadFile.url
  previewVisible.value = true
}

// Handle exceeding limit
const handleExceed = (files, uploadFiles) => {
  ElMessage.warning(`You can upload a maximum of ${props.limit} images`)
  emit('exceed', files, uploadFiles)
}
</script>

<style lang="scss" scoped>
.image-upload-container {
  .hide-upload {
    :deep(.el-upload--picture-card) {
      display: none;
    }
  }
  
  .upload-icon {
    font-size: 28px;
    color: #8c939d;
  }
  
  .upload-text {
    color: #8c939d;
    margin-top: 8px;
  }
  
  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  display: block;
  margin: 0 auto;
}
</style> 