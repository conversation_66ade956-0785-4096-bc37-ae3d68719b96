import request from '@/utils/request'

/**
 * 上传图片
 * @param {File} file 图片文件
 * @param {Object} data 附加信息
 * @returns {Promise}
 */
export function uploadImage(file, data = {}) {
  const formData = new FormData()
  formData.append('file', file)
  
  // 添加其他数据
  Object.keys(data).forEach(key => {
    formData.append(key, data[key])
  })
  
  return request({
    url: '/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传视频
 * @param {File} file 视频文件
 * @param {Object} data 附加信息
 * @returns {Promise}
 */
export function uploadVideo(file, data = {}) {
  const formData = new FormData()
  formData.append('file', file)
  
  // 添加其他数据
  Object.keys(data).forEach(key => {
    formData.append(key, data[key])
  })
  
  return request({
    url: '/upload/video',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传多个图片文件
 * @param {File[]} files 图片文件数组
 * @param {Object} data 附加信息
 * @returns {Promise}
 */
export function uploadMultipleImages(files, data = {}) {
  const formData = new FormData()
  
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file)
  })
  
  // 添加其他数据
  Object.keys(data).forEach(key => {
    formData.append(key, data[key])
  })
  
  return request({
    url: '/upload/images',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取上传进度
 * @param {string} taskId 上传任务ID
 * @returns {Promise}
 */
export function getUploadProgress(taskId) {
  return request({
    url: `/upload/progress/${taskId}`,
    method: 'get'
  })
}

/**
 * 取消上传
 * @param {string} taskId 上传任务ID
 * @returns {Promise}
 */
export function cancelUpload(taskId) {
  return request({
    url: `/upload/cancel/${taskId}`,
    method: 'delete'
  })
} 