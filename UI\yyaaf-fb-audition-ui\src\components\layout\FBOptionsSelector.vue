<template>
  <div class="fb-selectors">
    <div class="selector-item">
      <el-select
        v-model="selectedFBUserId"
        placeholder="Select FB User"
        clearable
        filterable
        style="width: 180px"
        :class="{ 'no-user-selected': !selectedFBUserId }"
        @change="handleFBUserChange"
      >
        <template #prefix>
          <el-icon><User /></el-icon>
        </template>
        <el-option
          v-for="user in fbUserList"
          :key="user.id"
          :label="user.name"
          :value="user.id"
        >
          <div class="fb-user-option">
            <el-avatar :size="20" :src="user.avatar">
              {{ user.name ? user.name.charAt(0) : 'U' }}
            </el-avatar>
            <span class="option-name">{{ user.name }}</span>
            <el-tag
              :type="user.oAuthState === 1 ? 'success' : user.oAuthState === 9 ? 'warning' : 'danger'"
              size="small"
            >
              {{ user.oAuthState === 1 ? 'Authorized' : user.oAuthState === 9 ? 'Expired' : 'Unauthorized' }}
            </el-tag>
          </div>
        </el-option>
      </el-select>
      <!-- Red border reminder when no user selected -->
      <div v-if="!selectedFBUserId && fbUserList && fbUserList.length > 0" class="selection-reminder">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <span>Please select a FB user</span>
      </div>
    </div>
    
    <div class="selector-item">
      <el-select
        v-model="selectedFBAdAccountId"
        placeholder="Select Ad Account"
        clearable
        filterable
        style="width: 200px"
        :class="{ 'no-account-selected': !selectedFBAdAccountId && selectedFBUserId }"
        :disabled="!selectedFBUserId"
        @change="handleFBAdAccountChange"
      >
        <template #prefix>
          <el-icon><CreditCard /></el-icon>
        </template>
        <el-option
          v-for="account in fbAdAccountList"
          :key="account.value"
          :label="account.label"
          :value="account.value"
        >
          <div class="fb-account-option">
            <span class="option-name">{{ account.label }}</span>
          </div>
        </el-option>
      </el-select>
      <!-- Red border reminder when no ad account selected -->
      <div v-if="!selectedFBAdAccountId && selectedFBUserId && fbAdAccountList && fbAdAccountList.length > 0" class="selection-reminder">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <span>Please select an ad account</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { User, CreditCard, Warning } from '@element-plus/icons-vue'

const store = useStore()

// FB option states
const selectedFBUserId = ref(null)
const selectedFBAdAccountId = ref(null) 

// FB option lists
const fbUserList = computed(() => store.getters.fbUserList)
const fbAdAccountList = computed(() => store.getters.fbAdAccountList)

// Initialize FB options
onMounted(async () => {
  try {
    // Get current selected values (restore from localStorage)
    selectedFBUserId.value = store.getters.fbUserId
    selectedFBAdAccountId.value = store.getters.fbAdAccountId
    
    // Load FB user list
    await store.dispatch('loadFBUserList')
    
    // If a user is already selected, load the corresponding ad account list
    if (selectedFBUserId.value) {
      await store.dispatch('loadFBAdAccountList')
      
      // Sync ad account selection state
      if (selectedFBAdAccountId.value) {
        // Ensure ad account list is loaded before setting selected state
        setTimeout(() => {
          const account = store.getters.fbAdAccountList.find(acc => acc.value === selectedFBAdAccountId.value)
          if (!account) {
            // If ad account does not exist, clear selection
            selectedFBAdAccountId.value = null
            store.dispatch('setFBAdAccount', null)
          }
        }, 100)
      }
    }
  } catch (error) {
    console.error('Failed to initialize FB options:', error)
    ElMessage.error('Failed to initialize FB options')
  }
})

// Watch for changes in store state and sync to local variables
watch(() => store.getters.fbUserId, (newValue) => {
  selectedFBUserId.value = newValue
})

watch(() => store.getters.fbAdAccountId, (newValue) => {
  selectedFBAdAccountId.value = newValue
})

// Handle FB user selection change
const handleFBUserChange = async (userId) => {
  try {
    await store.dispatch('setFBUser', userId)
    selectedFBAdAccountId.value = null // Clear ad account selection
  } catch (error) {
    console.error('Failed to switch FB user:', error)
    ElMessage.error('Failed to switch FB user')
  }
}

// Handle FB ad account selection change
const handleFBAdAccountChange = (accountId) => {
  try {
    store.dispatch('setFBAdAccount', accountId)
    window.location.reload()
  } catch (error) {
    console.error('Failed to switch ad account:', error)
    ElMessage.error('Failed to switch ad account')
  }
}
</script>

<style lang="scss" scoped>
.fb-selectors {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .selector-item {
    position: relative;
    
    .el-select {
      :deep(.el-input__wrapper) {
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s;
        
        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        &.is-focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
      
      // Red border when no user selected
      &.no-user-selected {
        :deep(.el-input__wrapper) {
          border: 2px solid #f56c6c;
          box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
          animation: pulse-red 2s infinite;
        }
      }
      
      // Red border when no ad account selected
      &.no-account-selected {
        :deep(.el-input__wrapper) {
          border: 2px solid #f56c6c;
          box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
          animation: pulse-red 2s infinite;
        }
      }
    }
    
    // Selection reminder tooltip
    .selection-reminder {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #f56c6c;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      z-index: 1000;
      margin-top: 2px;
      
      .warning-icon {
        font-size: 14px;
      }
      
      // Arrow pointing up
      &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 12px;
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid #f56c6c;
      }
    }
  }
}

// Pulse animation for red border
@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.4);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
  }
}

// FB option styles
.fb-user-option {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .option-name {
    flex: 1;
    font-size: 13px;
  }
  
  .el-tag {
    font-size: 10px;
  }
}

.fb-account-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .option-name {
    flex: 1;
    font-size: 13px;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .fb-selectors {
    gap: 8px;
    
    .selector-item {
      .el-select {
        width: 150px !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .fb-selectors {
    display: none; // Hide FB options on small screens
  }
}
</style>