/**
 * 格式化日期
 * @param {Date|string|number} date 日期对象或时间戳
 * @param {string} format 格式化模板，默认 YYYY-MM-DD HH:mm:ss
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hour = d.getHours()
  const minute = d.getMinutes()
  const second = d.getSeconds()
  
  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, month.toString().padStart(2, '0'))
    .replace(/DD/g, day.toString().padStart(2, '0'))
    .replace(/HH/g, hour.toString().padStart(2, '0'))
    .replace(/mm/g, minute.toString().padStart(2, '0'))
    .replace(/ss/g, second.toString().padStart(2, '0'))
}

/**
 * 格式化数字，千分位分隔
 * @param {number} num 需要格式化的数字
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, decimals = 2) {
  if (num === null || num === undefined) return '0'
  
  return Number(num).toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化日期时间
 * @param {string|Date} date 日期对象或日期字符串
 * @param {boolean} showTime 是否显示时间
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(date, showTime = true) {
  if (!date) return '';

  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  if (showTime) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  
  return `${year}-${month}-${day}`;
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化货币
 * @param {number} amount 金额
 * @param {string} currency 货币符号
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的货币字符串
 */
export function formatCurrency(amount, currency = '¥', decimals = 2) {
  if (amount === undefined || amount === null) return '';
  
  return currency + amount.toFixed(decimals).replace(/\d(?=(\d{3})+\.)/g, '$&,');
}

/**
 * 格式化百分比
 * @param {number} value 值
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercent(value, decimals = 2) {
  if (value === undefined || value === null) return '';
  
  return (value * 100).toFixed(decimals) + '%';
}

/**
 * 格式化视频时长
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的时长字符串，如 "01:23:45"
 */
export function formatDuration(seconds) {
  if (!seconds) return '00:00';
  
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  let result = '';
  
  if (hrs > 0) {
    result += String(hrs).padStart(2, '0') + ':';
  }
  
  result += String(mins).padStart(2, '0') + ':' + String(secs).padStart(2, '0');
  
  return result;
}

/**
 * 计算点击率 (CTR)
 * @param {number} clicks 点击数
 * @param {number} impressions 展示数
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的点击率，带%
 */
export function calculateCTR(clicks, impressions, decimals = 2) {
  if (!clicks || !impressions) return '0%'
  
  const ctr = (clicks / impressions) * 100
  return ctr.toFixed(decimals) + '%'
}

/**
 * 计算转化率 (CVR)
 * @param {number} conversions 转化数
 * @param {number} clicks 点击数
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的转化率，带%
 */
export function calculateCVR(conversions, clicks, decimals = 2) {
  if (!conversions || !clicks) return '0%'
  
  const cvr = (conversions / clicks) * 100
  return cvr.toFixed(decimals) + '%'
} 