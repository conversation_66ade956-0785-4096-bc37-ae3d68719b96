<template>
  <div class="adset-list-container">
    <div class="adset-header">
      <h2>Ad Set Management</h2>
      <div class="adset-actions">
        <el-button type="primary" @click="goToCreate">Create Ad Set</el-button>
        <el-button type="danger" :disabled="!hasSelected" @click="handleBatchDelete">Batch Delete</el-button>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="Ad Set Name">
          <el-input v-model="searchForm.name" placeholder="Please enter ad set name" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="Campaign">
          <el-select v-model="searchForm.campaign_id" placeholder="Please select campaign" clearable filterable>
            <el-option 
              v-for="item in campaignOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Status">
          <el-select v-model="searchForm.status" placeholder="Please select status" clearable style="width: 200px">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
          <el-button @click="resetSearch">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="sync-container" style="margin-bottom: 15px;">
      <el-button type="primary" @click="handleSyncAdsets">
        <el-icon><Refresh /></el-icon>Sync Adsets
      </el-button>
    </div>
    
    <!-- Table Pagination Component -->
    <table-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :table-data="adsetList"
      :loading="loading"
      :total="pagination.total"
      @selection-change="handleSelectionChange"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="Ad Set ID" width="180" />
      <el-table-column prop="name" label="Ad Set Name" min-width="180" show-overflow-tooltip />
      <el-table-column prop="campaign_name" label="Campaign" min-width="180" show-overflow-tooltip />
      <el-table-column prop="daily_budget" label="Daily Budget" width="120">
        <template #default="scope">
          {{ formatCurrency(scope.row.daily_budget) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="Status" width="120">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="'ACTIVE'"
            :inactive-value="'PAUSED'"
            @change="(val) => handleStatusChange(scope.row.id, val)"
          />
          <span class="status-text">{{ scope.row.status === 'ACTIVE' ? 'Active' : 'Paused' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Targeting Summary" min-width="240" show-overflow-tooltip>
        <template #default="scope">
          <div v-if="scope.row.targetingObj" class="targeting-summary">
            <el-tag v-if="scope.row.targetingObj.geo_locations.countries" size="small" class="targeting-tag">
              Country: {{ formatCountries(scope.row.targetingObj.geo_locations.countries) }}
            </el-tag>
            <el-tag v-if="scope.row.targetingObj.age_min && scope.row.targetingObj.age_max" size="small" class="targeting-tag">
              Age: {{ scope.row.targetingObj.age_min }} - {{ scope.row.targetingObj.age_max }}
            </el-tag>
            <el-tag v-if="scope.row.targetingObj.genders" size="small" class="targeting-tag">
              Gender: {{ formatGenders(scope.row.targetingObj.genders) }}
            </el-tag>
            <!--<el-tag v-if="scope.row.targetingObj.interests && scope.row.targetingObj.interests.length" size="small" class="targeting-tag">
              Interests: {{ scope.row.targetingObj.interests.length }} items
            </el-tag>-->
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!--<el-table-column prop="impressions" label="展示量" width="120" />
      <el-table-column prop="clicks" label="点击量" width="120" />
      <el-table-column prop="ctr" label="点击率" width="100">
        <template #default="scope">
          {{ formatPercentage(scope.row.ctr) }}
        </template>
      </el-table-column>-->
      <el-table-column prop="created_time" label="Created Time" width="180" />
      <el-table-column label="Actions" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="goToEdit(scope.row.id)">Edit</el-button>
          <el-button type="primary" link @click="goToAds(scope.row.id)">Ads</el-button>
          <el-popconfirm title="Are you sure you want to delete this ad set?" @confirm="handleDelete(scope.row.id)">
            <template #reference>
              <el-button type="danger" link>Delete</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </table-pagination>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdsetList, deleteAdset, updateAdsetStatus, getSyncAdsets } from '@/api/adset'
import { getCampaignOptions } from '@/api/campaign'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'AdSetList',
  
  components: {
    TablePagination
  },
  
  data() {
    return {
      // Status options
      statusOptions: [
        { value: 'ACTIVE', label: 'Active' },
        { value: 'PAUSED', label: 'Paused' },
        { value: 'ARCHIVED', label: 'Archived' }
      ],
      
      // Campaign options
      campaignOptions: [],
      
      // Table data
      adsetList: [],
      loading: false,
      selectedRows: [],
      
      // Search conditions
      searchForm: {
        name: '',
        campaignId: '',
        status: ''
      },
      
      // Pagination info
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  
  computed: {
    // Check if any row is selected
    hasSelected() {
      return this.selectedRows.length > 0
    },
    
    // Check if viewing under specific campaign
    preselectedCampaignId() {
      return this.$route.query.campaignId || this.$route.params.campaignId
    }
  },

  watch: {
    // Watch preselected ad set ID
    preselectedCampaignId: {
      handler(newVal) {
        if (newVal) {
          this.searchForm.campaign_id = newVal
        }
      },
      immediate: true
    }
  },
  
  mounted() {
    this.fetchCampaignOptions()
    this.fetchAdsetList()
  },
  
  methods: {
    // Format currency
    formatCurrency(value) {
      if (!value || value == 0) return 'Campaign Budget'
      return `¥${Number(value).toFixed(2)}`
    },
    
    // Format percentage
    formatPercentage(value) {
      if (value === undefined || value === null) return '-'
      return `${(Number(value) * 100).toFixed(2)}%`
    },
    
    // Format countries
    formatCountries(countries) {
      if (!countries || !countries.length) return '-'
      if (countries.length <= 2) return countries.join(', ')
      return `${countries[0]} and ${countries.length - 1} other countries`
    },
    
    // Format gender
    formatGenders(genders) {
      if (!genders || !genders.length) return 'All'
      
      const genderMap = {
        '1': 'Male',
        '2': 'Female'
      }
      
      return genders.map(g => genderMap[g] || g).join(', ')
    },
    
    // Get campaign options
    async fetchCampaignOptions() {
      try {
        const res = await getCampaignOptions()
        if(res.code === 0) {
          this.campaignOptions = res.data || []
        }
      } catch (error) {
        console.error('Failed to get campaign list:', error)
        ElMessage.error('Failed to get campaign list')
      }
    },
    
    // Get ad set list
    async fetchAdsetList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize,
          name: this.searchForm.name || undefined,
          status: this.searchForm.status || undefined,
          campaign_id: this.searchForm.campaign_id || undefined
        }

        const res = await getAdsetList(params)
        if(res.code === 0) {
          this.adsetList = res.data
          this.pagination.total = res.total
        }
        else {
          ElMessage.error(res.msg)
        }

      } catch (error) {
        console.error('Failed to get ad set list:', error)
        ElMessage.error('Failed to get ad set list')
      } finally {
        this.loading = false
      }
    },
    
    // Handle search
    handleSearch() {
      this.pagination.currentPage = 1
      this.fetchAdsetList()
    },
    
    // Reset search
    resetSearch() {
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.pagination.currentPage = 1
      this.fetchAdsetList()
    },
    
    // Page number change
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.fetchAdsetList()
    },
    
    // Page size change
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.fetchAdsetList()
    },
    
    // Selection change
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },
    
    // Go to create page
    goToCreate() {
      // If under specific campaign, include campaign ID
      if (this.campaignId) {
        this.$router.push(`/campaigns/${this.campaignId}/adsets/create`)
      } else {
        this.$router.push('/adsets/create')
      }
    },
    
    // Go to edit page
    goToEdit(id) {
      this.$router.push(`/adsets/edit/${id}`)
    },
    
    // Go to ads page
    goToAds(id) {
      this.$router.push(`/ads/list?adsetId=${id}`)
    },
    
    // Delete ad set
    async handleDelete(id) {
      try {
        const res = await deleteAdset({id:id})
        if(res.code === 0) {
          this.fetchAdsetList()
          ElMessage.success('Successfully deleted')
        }
        else {
          ElMessage.error(res.msg)
        }
        
      } catch (error) {
        console.error('Failed to delete ad set:', error)
        ElMessage.error('Failed to delete ad set')
      }
    },
    
    // Batch delete
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        return ElMessage.warning('Please select ad sets to delete')
      }

      ElMessageBox.confirm('Are you sure you want to delete the selected ad sets?', 'Confirm Delete', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(async () => {
        const ids = this.selectedRows.map(item => item.id)
        
        try {
          const res = await Promise.all(ids.map(id => deleteAdset({id:id})))
          const allSuccess = res.every(item => item.code === 0)
          if (allSuccess) {
            ElMessage.success('Batch deletion successful')
          } else {
            const successCount = res.filter(item => item.code === 0).length
            ElMessage.warning(`Partial success: ${successCount}/${res.length} deleted successfully`)
          }
          this.fetchAdsetList()
        } catch (error) {
          console.error('Failed to batch delete ad sets:', error)
          ElMessage.error('Failed to batch delete ad sets')
        }
      })
    },
    
    // Update ad set status
    async handleStatusChange(id, status) {
      try {
        const statusText = status === 'ACTIVE' ? 'activate' : 'pause'
        const confirmMessage = `Are you sure you want to ${statusText} this ad set?`
        
        try {
          await ElMessageBox.confirm(confirmMessage, 'Confirm Operation', {
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            type: 'warning'
          })
          
          const res = await updateAdsetStatus(id, status)
          if(res.code === 0) {
            ElMessage.success('Status updated successfully')
            this.fetchAdsetList()
          }
          else {
            ElMessage.error(res.msg)
          }
        } catch (cancelError) {
          this.fetchAdsetList()
        }
      } catch (error) {
        console.error('Failed to update ad set status:', error)
        ElMessage.error('Failed to update ad set status')
        this.fetchAdsetList()
      }
    },
    
    // 同步广告组
    async handleSyncAdsets() {
      ElMessage.info('Syncing adsets...')
      const res = await getSyncAdsets()
      if(res.code === 0){
        ElMessage.success('Sync adsets successfully')
        this.fetchAdsetList()
      }
      else{
        ElMessage.error(res.msg)
      }
    }
  }
}
</script>

<style scoped>
.adset-list-container {
  padding: 20px;
}

.adset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-container {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.status-text {
  margin-left: 8px;
  font-size: 12px;
}

.targeting-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.targeting-tag {
  margin-right: 5px;
}
</style> 