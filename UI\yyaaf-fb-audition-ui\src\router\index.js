import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/layout/Layout.vue'
import { getToken } from '@/utils/auth' // get token from cookie
import store from '@/store'
import { ElMessage } from 'element-plus'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/Login.vue'),
    meta: { title: 'Login', hidden: true }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '404', hidden: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: '/',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: { title: 'Dashboard', icon: 'monitor' }
      }
    ]
  },
  {
    path: '/analytics',
    component: Layout,
    redirect: '/analytics/campaign',
    meta: { title: 'Data Analysis', icon: 'DataAnalysis' },
    children: [
      {
        path: 'campaign',
        name: 'CampaignAnalytics',
        component: () => import('@/views/analytics/CampaignAnalytics.vue'),
        meta: { title: 'Campaign Analytics' }
      },
      {
        path: 'adset',
        name: 'AdSetAnalytics',
        component: () => import('@/views/analytics/AdSetAnalytics.vue'),
        meta: { title: 'Ad Set Analytics' }
      },
      {
        path: 'ad',
        name: 'AdAnalytics',
        component: () => import('@/views/analytics/AdAnalytics.vue'),
        meta: { title: 'Ad Analytics' }
      }
    ]
  },
  {
    path: '/media',
    component: Layout,
    redirect: '/media/image',
    meta: { title: 'Media Library', icon: 'PictureRounded' },
    children: [
      {
        path: 'image',
        name: 'ImageLibrary',
        component: () => import('@/views/media/ImageLibrary.vue'),
        meta: { title: 'Image Library' }
      },
      {
        path: 'video',
        name: 'VideoLibrary',
        component: () => import('@/views/media/VideoLibrary.vue'),
        meta: { title: 'Video Library' }
      }
    ]
  },
  {
    path: '/creatives',
    component: Layout,
    redirect: '/creatives/list',
    meta: { title: 'Ad Creatives', icon: 'Brush' },
    children: [
      {
        path: 'list',
        name: 'CreativeList',
        component: () => import('@/views/creatives/CreativeList.vue'),
        meta: { title: 'Creative List' }
      },
      {
        path: 'create',
        name: 'CreativeCreate',
        component: () => import('@/views/creatives/CreativeForm.vue'),
        meta: { title: 'Create Creative' }
      },
      {
        path: 'edit/:id',
        name: 'CreativeEdit',
        component: () => import('@/views/creatives/CreativeForm.vue'),
        meta: { title: 'Edit Creative', hidden: true }
      }
    ]
  },
  {
    path: '/campaigns',
    component: Layout,
    redirect: '/campaigns/list',
    meta: { title: 'Campaigns', icon: 'Promotion' },
    children: [
      {
        path: 'list',
        name: 'CampaignList',
        component: () => import('@/views/campaigns/CampaignList.vue'),
        meta: { title: 'Campaign List' }
      },
      {
        path: 'create',
        name: 'CampaignCreate',
        component: () => import('@/views/campaigns/CampaignForm.vue'),
        meta: { title: 'Create Campaign' }
      },
      {
        path: 'edit/:id',
        name: 'CampaignEdit',
        component: () => import('@/views/campaigns/CampaignForm.vue'),
        meta: { title: 'Edit Campaign', hidden: true }
      }
    ]
  },
  {
    path: '/adsets',
    component: Layout,
    redirect: '/adsets/list',
    meta: { title: 'Ad Sets', icon: 'SetUp' },
    children: [
      {
        path: 'list',
        name: 'AdSetList',
        component: () => import('@/views/adsets/AdSetList.vue'),
        meta: { title: 'Ad Set List' }
      },
      {
        path: 'create',
        name: 'AdSetCreate',
        component: () => import('@/views/adsets/AdSetForm.vue'),
        meta: { title: 'Create Ad Set' }
      },
      {
        path: 'edit/:id',
        name: 'AdSetEdit',
        component: () => import('@/views/adsets/AdSetForm.vue'),
        meta: { title: 'Edit Ad Set', hidden: true }
      }
    ]
  },
  {
    path: '/ads',
    component: Layout,
    redirect: '/ads/list',
    meta: { title: 'Ads', icon: 'PictureFilled' },
    children: [
      {
        path: 'list',
        name: 'AdList',
        component: () => import('@/views/ads/AdList.vue'),
        meta: { title: 'Ad List' }
      },
      {
        path: 'create',
        name: 'AdCreate',
        component: () => import('@/views/ads/AdForm.vue'),
        meta: { title: 'Create Ad' }
      },
      {
        path: 'edit/:id',
        name: 'AdEdit',
        component: () => import('@/views/ads/AdForm.vue'),
        meta: { title: 'Edit Ad', hidden: true }
      }
    ]
  },
  {
    path: '/fbuser',
    component: Layout,
    redirect: '/fbuser/list',
    meta: { title: 'FB User Management', icon: 'User' },
    children: [
      {
        path: 'list',
        name: 'FBUserList',
        component: () => import('@/views/fbuser/FBUserList.vue'),
        meta: { title: 'FB User List' }
      },
      {
        path: 'pages',
        name: 'FBPageList',
        component: () => import('@/views/fbpage/FBPageList.vue'),
        meta: { title: 'FB Page List', hidden: true }
      }
    ]
  },
  {
    path: '/fbadaccount',
    component: Layout,
    redirect: '/fbadaccount/list',
    meta: { title: 'FB Ad Account', icon: 'CreditCard', hidden: true },
    children: [
      {
        path: 'list',
        name: 'FBAdAccountList',
        component: () => import('@/views/fbadaccount/FBAdAccountList.vue'),
        meta: { title: 'FB Ad Account List', hidden: true }
      }
    ]
  },
  { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true, meta: { hidden: true } }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title} - FB Ad Management` || 'FB Ad Management'
  
  // 简单的登录验证
  //const token = localStorage.getItem('token')
  const token = getToken()
  const whiteList = ['/login']
  
  if (token) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      if(store.getters.user == null) {
        await store.dispatch('getInfo')
      }
      if(store.getters.userOAthState == null || store.getters.userOAthState == false) {
        await store.dispatch('getUserOAthState')
      }
      if(to.path != '/fbuser/list' && store.getters.userOAthState == false) {
        ElMessage.error('No FB Users available, please authorize')
        next('/fbuser/list')
        return
      }
      if(to.path != '/fbuser/list' && (store.getters.fbUserId == null || store.getters.fbAdAccountId == null)) {
        ElMessage.error('No FB Users or Ad Accounts available, please select a user and ad account')
        next('/fbuser/list')
        return
      }
      next()
    }
  } else {
    if (whiteList.includes(to.path)) {
      next()
    } else {
      next('/login')
    }
  }
})

export default router