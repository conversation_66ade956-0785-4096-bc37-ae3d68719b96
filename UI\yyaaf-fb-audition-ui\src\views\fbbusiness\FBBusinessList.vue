<template>
  <div class="fb-business-container">
    <!-- Page title and action bar -->
    <div class="page-header">
      <div class="header-left">
        <h2>FB Business List</h2>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshList">
          <el-icon><Refresh /></el-icon>
          Refresh
        </el-button>
      </div>
    </div>

    <!-- Search bar -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="Business Name">
          <el-input
            v-model="searchForm.name"
            placeholder="Enter business name"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="Status">
          <el-select
            v-model="searchForm.status"
            placeholder="Select status"
            clearable
            style="width: 150px;"
          >
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Industry">
          <el-select
            v-model="searchForm.industry"
            placeholder="Select industry"
            clearable
            style="width: 150px;"
          >
            <el-option v-for="item in industryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
          <el-button @click="resetSearch">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Business list table -->
    <div class="table-container">
      <TablePagination
        :table-data="businessList"
        :loading="loading"
        :total="pagination.total"
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="id" label="Business ID" min-width="150" />

        <el-table-column prop="name" label="Business Info" min-width="250">
          <template #default="{ row }">
            <div class="business-info">
              <el-avatar
                :size="40"
                :src="generateBusinessAvatar(row.name)"
                class="business-avatar"
                shape="square"
              >
                {{ row.name ? row.name.charAt(0).toUpperCase() : 'B' }}
              </el-avatar>
              <div class="business-details">
                <div class="business-name">{{ row.name }}</div>
                <div class="business-id">FB ID: {{ row.businessId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="industry" label="Industry" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getIndustryTagType(row.industry)" size="medium">
              {{ row.industry }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="Status" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="medium"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="verificationStatus" label="Verification" min-width="120">
          <template #default="{ row }">
            <el-tag
              :type="getVerificationStatusType(row.verificationStatus)"
              size="medium"
            >
              {{ getVerificationStatusText(row.verificationStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="country" label="Country" min-width="80" />

        <el-table-column prop="currency" label="Currency" min-width="80" />

        <el-table-column label="Resources" min-width="150">
          <template #default="{ row }">
            <div class="resource-stats">
              <el-tag size="small" type="info">{{ row.adAccountsCount }} Accounts</el-tag>
              <el-tag size="small" type="success">{{ row.pagesCount }} Pages</el-tag>
              <el-tag size="small" type="warning">{{ row.pixelsCount }} Pixels</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="lastUpdateTime"
          label="Last Update Time"
          align="center"
          min-width="150"
        ></el-table-column>

        <el-table-column label="Actions" min-width="300" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewAdAccounts(row)"
            >
              Ad Accounts
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewPages(row)"
            >
              Pages
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="viewAdsPixels(row)"
            >
              AdsPixels
            </el-button>
          </template>
        </el-table-column>
      </TablePagination>
    </div>
  </div>
</template>

<script>
import { getFBBusinessList } from '@/api/fbbusiness'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import TablePagination from '@/components/table/TablePagination.vue'

export default {
  name: 'FBBusinessList',
  components: {
    Refresh,
    TablePagination
  },
  data() {
    return {
      loading: false,
      businessList: [],
      selectedBusinesses: [],
      searchForm: {
        name: '',
        status: '',
        industry: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      statusOptions: [
        { label: 'Active', value: 'ACTIVE' },
        { label: 'Inactive', value: 'INACTIVE' },
        { label: 'Pending', value: 'PENDING' }
      ],
      industryOptions: [
        { label: 'E-commerce', value: 'E-commerce' },
        { label: 'Fashion', value: 'Fashion' },
        { label: 'Technology', value: 'Technology' },
        { label: 'Gaming', value: 'Gaming' },
        { label: 'Finance', value: 'Finance' },
        { label: 'Health', value: 'Health' },
        { label: 'Education', value: 'Education' },
        { label: 'Entertainment', value: 'Entertainment' }
      ]
    }
  },
  mounted() {
    this.fetchBusinessList()
  },
  methods: {
    // Fetch business list
    async fetchBusinessList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          limit: this.pagination.size,
          ...this.searchForm
        }

        const response = await getFBBusinessList(params)

        if (response.code === 0) {
          this.businessList = response.data || []
          this.pagination.total = response.total || 0
        } else {
          ElMessage.error(response.message || 'Failed to fetch business list')
        }
      } catch (error) {
        console.error('Failed to fetch business list:', error)
        ElMessage.error('Failed to fetch business list')
      } finally {
        this.loading = false
      }
    },

    // Search
    handleSearch() {
      this.pagination.page = 1
      this.fetchBusinessList()
    },

    // Reset search
    resetSearch() {
      this.searchForm = {
        name: '',
        status: '',
        industry: ''
      }
      this.pagination.page = 1
      this.fetchBusinessList()
    },

    // Refresh list
    refreshList() {
      this.fetchBusinessList()
    },

    // Selection change
    handleSelectionChange(selection) {
      this.selectedBusinesses = selection
    },

    // Page size change
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchBusinessList()
    },

    // Current page change
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchBusinessList()
    },

    // View ad accounts
    viewAdAccounts(business) {
      // Navigate to FB ad account list page, pass business ID
      this.$router.push({
        name: 'FBAdAccountList',
        query: {
          businessId: business.id,
          businessName: business.name
        }
      })
    },

    // View pages
    viewPages(business) {
      // Navigate to FB page list page, pass business ID
      this.$router.push({
        name: 'FBPageList',
        query: {
          businessId: business.id,
          businessName: business.name
        }
      })
    },

    // View ads pixels
    viewAdsPixels(business) {
      // Navigate to FB ads pixels list page, pass business ID
      this.$router.push({
        name: 'FBAdsPixelsList',
        query: {
          businessId: business.id,
          businessName: business.name
        }
      })
    },

    // Generate business avatar URL
    generateBusinessAvatar(name) {
      const seed = encodeURIComponent(name || 'default')
      return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=409eff&textColor=ffffff`
    },

    // Get status type for tag
    getStatusType(status) {
      const statusMap = {
        'ACTIVE': 'success',
        'INACTIVE': 'danger',
        'PENDING': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // Get status text
    getStatusText(status) {
      const statusMap = {
        'ACTIVE': 'Active',
        'INACTIVE': 'Inactive',
        'PENDING': 'Pending'
      }
      return statusMap[status] || status
    },

    // Get verification status type for tag
    getVerificationStatusType(status) {
      const statusMap = {
        'VERIFIED': 'success',
        'UNVERIFIED': 'danger',
        'PENDING': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // Get verification status text
    getVerificationStatusText(status) {
      const statusMap = {
        'VERIFIED': 'Verified',
        'UNVERIFIED': 'Unverified',
        'PENDING': 'Pending'
      }
      return statusMap[status] || status
    },

    // Get industry tag type
    getIndustryTagType(industry) {
      const industryMap = {
        'E-commerce': 'primary',
        'Fashion': 'success',
        'Technology': 'info',
        'Gaming': 'warning',
        'Finance': 'danger',
        'Health': 'success',
        'Education': 'primary',
        'Entertainment': 'warning'
      }
      return industryMap[industry] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.fb-business-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;

  .el-form {
    margin: 0;
  }
}

.table-container {
  margin-bottom: 20px;
}

.business-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .business-avatar {
    flex-shrink: 0;
  }

  .business-details {
    flex: 1;
    min-width: 0;

    .business-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .business-id {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.resource-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .el-tag {
    margin: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .fb-business-container {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .header-left {
      h2 {
        font-size: 20px;
      }
    }
  }

  .search-bar {
    .el-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;

        .el-input, .el-select {
          width: 100%;
        }
      }
    }
  }

  .business-info {
    .business-avatar {
      width: 32px;
      height: 32px;
    }

    .business-details {
      .business-name {
        font-size: 14px;
      }

      .business-id {
        font-size: 11px;
      }
    }
  }

  .resource-stats {
    .el-tag {
      font-size: 10px;
      padding: 2px 4px;
    }
  }

  // Mobile operation button styles
  :deep(.el-table__fixed-right) {
    .el-button {
      margin: 2px;
      padding: 5px 8px;
      font-size: 12px;

      &.el-button--small {
        padding: 4px 6px;
      }
    }
  }
}
</style>