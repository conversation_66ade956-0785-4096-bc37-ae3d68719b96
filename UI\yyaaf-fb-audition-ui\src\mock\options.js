import Mock from 'mockjs'

// FB用户选项列表数据
const fbUserOptions = Mock.mock({
  'list|20': [
    {
      'id|+1': function() {
        return `fb_user_${this.id || 1}`
      },
      name: '@cname',
      'email': '@email',
      'status|1': [0, 1, 9], // 0=未授权, 1=已授权, 9=已过期
      'oAuthState|1': [0, 1, 9],
      avatar: function() {
        return `https://api.dicebear.com/7.x/avataaars/svg?seed=${this.name}`
      }
    }
  ]
})

// FB广告账户选项列表数据
const fbAdAccountOptions = Mock.mock({
  'list|50': [
    {
      'id': function() {
        return `act_${Mock.Random.string('number', 10, 15)}`
      },
      'userId': function() {
        // 关联到FB用户
        const userIndex = Mock.Random.integer(1, 20)
        return `fb_user_${userIndex}`
      },
      name: function() {
        return `${Mock.Random.pick(['东南亚', '欧美', '日韩', '拉美', '中东'])}广告账户_${Mock.Random.string('upper', 3, 5)}`
      },
      'currency|1': ['USD', 'EUR', 'GBP', 'JPY', 'CNY'],
      'status|1': ['ACTIVE', 'PAUSED', 'DISABLED'],
      'spendCap': '@integer(1000, 50000)',
      'balance': '@integer(100, 10000)',
      timezone: function() {
        return Mock.Random.pick([
          'Asia/Shanghai',
          'America/New_York', 
          'Europe/London',
          'Asia/Tokyo',
          'Europe/Berlin'
        ])
      },
      businessName: function() {
        return `${Mock.Random.cword(2, 4)}科技有限公司`
      },
      'createdTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
    }
  ]
})

// API接口mock
Mock.mock('/api/options/getFBUserOptions', 'get', (options) => {
  console.log('Mock getFBUserOptions called')
  
  return {
    code: 0,
    message: 'success',
    data: fbUserOptions.list.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      avatar: user.avatar,
      status: user.status,
      oAuthState: user.oAuthState,
      label: `${user.name} (${user.email})`,
      value: user.id
    }))
  }
})

Mock.mock(/\/api\/options\/getFBAdAccountOptions/, 'get', (options) => {
  console.log('Mock getFBAdAccountOptions called', options.url)
  
  // 从URL中获取fbUserId参数（如果有的话）
  const url = new URL(options.url, 'http://localhost')
  const fbUserId = url.searchParams.get('fbUserId')
  
  let filteredAccounts = fbAdAccountOptions.list
  
  // 如果指定了fbUserId，只返回该用户的广告账户
  if (fbUserId) {
    filteredAccounts = fbAdAccountOptions.list.filter(account => 
      account.userId === fbUserId || account.userId === parseInt(fbUserId)
    )
  }
  
  return {
    code: 0,
    message: 'success',
    data: filteredAccounts.map(account => ({
      id: account.id,
      userId: account.userId,
      name: account.name,
      currency: account.currency,
      status: account.status,
      spendCap: account.spendCap,
      balance: account.balance,
      timezone: account.timezone,
      businessName: account.businessName,
      createdTime: account.createdTime,
      label: `${account.name} (${account.id})`,
      value: account.id
    }))
  }
})

Mock.mock('/api/options/getFBCountryOptions', 'get', (options) => {
  console.log('Mock getFBCountryOptions called')
  
  const countries = [
    { code: 'US', name: '美国', label: '美国 (US)', value: 'US' },
    { code: 'CN', name: '中国', label: '中国 (CN)', value: 'CN' },
    { code: 'JP', name: '日本', label: '日本 (JP)', value: 'JP' },
    { code: 'KR', name: '韩国', label: '韩国 (KR)', value: 'KR' },
    { code: 'GB', name: '英国', label: '英国 (GB)', value: 'GB' },
    { code: 'DE', name: '德国', label: '德国 (DE)', value: 'DE' },
    { code: 'FR', name: '法国', label: '法国 (FR)', value: 'FR' },
    { code: 'CA', name: '加拿大', label: '加拿大 (CA)', value: 'CA' },
    { code: 'AU', name: '澳大利亚', label: '澳大利亚 (AU)', value: 'AU' },
    { code: 'BR', name: '巴西', label: '巴西 (BR)', value: 'BR' }
  ]
  
  return {
    code: 0,
    message: 'success',
    data: countries
  }
})

export default {
  fbUserOptions: fbUserOptions.list,
  fbAdAccountOptions: fbAdAccountOptions.list
} 