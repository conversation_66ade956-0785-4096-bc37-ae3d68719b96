<template>
  <div class="table-pagination-container">
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="Array.isArray(tableData) ? tableData : []"
      border
      v-bind="$attrs"
      @selection-change="handleSelectionChange"
    >
      <slot></slot>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="showPagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TablePagination',
  
  props: {
    // 表格数据
    tableData: {
      type: [Array, Object],
      required: true,
      default: () => []
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 总条数
    total: {
      type: Number,
      default: 0
    },
    // 当前页码
    currentPage: {
      type: Number,
      default: 1
    },
    // 每页条数
    pageSize: {
      type: Number,
      default: 10
    },
    // 每页条数选项
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    }
  },
  
  emits: [
    'update:currentPage',
    'update:pageSize',
    'selection-change',
    'size-change',
    'current-change'
  ],
  
  methods: {
    // 选择行变更
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    
    // 每页条数变更
    handleSizeChange(size) {
      this.$emit('update:pageSize', size)
      this.$emit('size-change', size)
    },
    
    // 页码变更
    handleCurrentChange(page) {
      this.$emit('update:currentPage', page)
      this.$emit('current-change', page)
    }
  }
}
</script>

<style scoped>
.table-pagination-container {
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 