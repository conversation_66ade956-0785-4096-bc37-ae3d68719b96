<template>
  <div class="campaign-form-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? 'Edit Campaign' : 'Create Campaign' }}</span>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            <span>Back</span>
          </el-button>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="200px"
        label-position="right"
        :disabled="loading"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">Basic Information</h3>
          
          <el-form-item label="Campaign ID" v-if="isEdit">
            <el-input v-model="formData.id" disabled />
          </el-form-item>

          <el-form-item label="Campaign Name" prop="name" required>
            <el-input 
              v-model="formData.name"
              placeholder="Please enter campaign name"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="Campaign Objective" prop="objective" required>
            <el-select
              v-model="formData.objective"
              placeholder="Please select campaign objective"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="item in objectiveOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">Campaign objective determines the final purpose of ad delivery, cannot be modified after creation</div>
          </el-form-item>
          
          <el-form-item label="Special Ad Categories" prop="special_ad_categories">
            <el-select
              v-model="formData.special_ad_categories"
              placeholder="Please select special ad categories"
              style="width: 100%"
              multiple
              clearable
            >
              <el-option
                v-for="item in specialAdCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">If your ad belongs to special categories, please select them</div>
          </el-form-item>
          
          <el-form-item label="Buying Type" prop="buying_type" required>
            <el-select
              v-model="formData.buying_type"
              placeholder="Please select buying type"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="item in buyingTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">Buying type determines how ads are charged, cannot be modified after creation</div>
          </el-form-item>
          
          <!--<div class="budget-row">
            <el-form-item label="每日预算" prop="daily_budget" class="budget-item">
              <el-input-number
                v-model="formData.daily_budget"
                :min="1"
                :precision="2"
                :step="10"
                style="width: 100%"
              />
              <div class="form-tip">设置每日预算上限，单位：美元</div>
            </el-form-item>
            
            <el-form-item label="总预算上限" prop="spend_cap" class="budget-item">
              <el-input-number
                v-model="formData.spend_cap"
                :min="0"
                :precision="2"
                :step="100"
                style="width: 100%"
              />
              <div class="form-tip">设置广告系列总预算上限，0表示无限制，单位：美元</div>
            </el-form-item>
          </div>-->
          
          <el-form-item label="Status" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio-button label="ACTIVE">Active</el-radio-button>
              <el-radio-button label="PAUSED">Paused</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
        
        <!-- 投放设置 -->
        <!--<div class="form-section">
          <h3 class="section-title">投放设置</h3>
          
          <el-form-item label="开始时间" prop="start_time">
            <el-date-picker
              v-model="formData.start_time"
              type="datetime"
              placeholder="选择开始时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="结束时间" prop="stop_time">
            <el-date-picker
              v-model="formData.stop_time"
              type="datetime"
              placeholder="选择结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
            <div class="form-tip">不设置结束时间表示持续投放</div>
          </el-form-item>
        </div>-->
        
        <!-- 优化设置 -->
        <!--<div class="form-section">
          <h3 class="section-title">优化设置</h3>
          
          <el-form-item label="优化目标" prop="optimization_goal">
            <el-select
              v-model="formData.optimization_goal"
              placeholder="请选择优化目标"
              style="width: 100%"
            >
              <el-option
                v-for="item in optimizationGoalOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-tip">根据广告目标不同，可选择的优化目标也会有所不同</div>
          </el-form-item>
          
          <el-form-item label="计费事件" prop="billing_event">
            <el-select
              v-model="formData.billing_event"
              placeholder="请选择计费事件"
              style="width: 100%"
            >
              <el-option
                v-for="item in billingEventOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>-->
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">{{ isEdit ? 'Save Changes' : 'Create Campaign' }}</el-button>
          <el-button @click="resetForm" :disabled="loading">Reset</el-button>
          <el-button @click="goBack" :disabled="loading">Cancel</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getCampaignDetail, 
  createCampaign, 
  updateCampaign, 
  getCampaignObjectives, 
  getBuyingTypes, 
  getSpecialAdCategories 
} from '@/api/campaign'

export default {
  name: 'CampaignForm',
  
  data() {
    // 日期范围验证
    const validateDateRange = (rule, value, callback) => {
      if (this.formData.start_time && this.formData.stop_time) {
        if (new Date(this.formData.start_time) >= new Date(this.formData.stop_time)) {
          callback(new Error('End time must be later than start time'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    
    return {
      loading: false,
      formRef: null,
      
      // 表单数据
      formData: {
        id: '',
        name: '',
        objective: '',
        buying_type: 'AUCTION',
        special_ad_categories: ['NONE'],
        daily_budget: 0,
        spend_cap: 0,
        status: 'PAUSED',
        start_time: '',
        stop_time: '',
        optimization_goal: '',
        billing_event: 'IMPRESSIONS'
      },
      
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: 'Please enter campaign name', trigger: 'blur' },
          { min: 2, max: 50, message: 'Length should be between 2 and 50 characters', trigger: 'blur' }
        ],
        special_ad_categories: [
          { required: true, message: 'Please select special ad categories', trigger: 'change' }
        ],
        objective: [
          { required: true, message: 'Please select campaign objective', trigger: 'change' }
        ],
        buying_type: [
          { required: true, message: 'Please select buying type', trigger: 'change' }
        ],
        daily_budget: [
          { required: true, message: 'Please set daily budget', trigger: 'blur' },
          //{ type: 'number', min: 1, message: '每日预算必须大于0', trigger: 'blur' }
        ],
        start_time: [
          { validator: validateDateRange, trigger: 'change' }
        ],
        stop_time: [
          { validator: validateDateRange, trigger: 'change' }
        ]
      },
      
      // 选项数据
      objectiveOptions: [],
      buyingTypeOptions: [],
      specialAdCategoryOptions: [],
      optimizationGoalOptions: [
        { value: 'REACH', label: 'Reach' },
        { value: 'IMPRESSIONS', label: 'Impressions' },
        { value: 'LINK_CLICKS', label: 'Link Clicks' },
        { value: 'APP_INSTALLS', label: 'App Installs' },
        { value: 'VIDEO_VIEWS', label: 'Video Views' },
        { value: 'LEAD_GENERATION', label: 'Lead Generation' },
        { value: 'CONVERSIONS', label: 'Conversions' },
        { value: 'LANDING_PAGE_VIEWS', label: 'Landing Page Views' }
      ],
      billingEventOptions: [
        { value: 'IMPRESSIONS', label: 'Impressions' },
        { value: 'LINK_CLICKS', label: 'Clicks' },
        { value: 'APP_INSTALLS', label: 'App Installs' },
        { value: 'CONVERSIONS', label: 'Conversions' }
      ]
    }
  },
  
  computed: {
    // 判断是否是编辑模式
    isEdit() {
      return this.$route.params.id !== undefined
    }
  },
  
  methods: {
    // 获取下拉选项数据
    async fetchOptions() {
      try {
        // 实际项目中应该从API获取，这里使用模拟数据
        this.objectiveOptions = [
          { value: 'OUTCOME_TRAFFIC', label: 'Drive Traffic' },
          { value: 'OUTCOME_LEADS', label: 'Generate Leads' },
          { value: 'OUTCOME_SALES', label: 'Promote Sales' },
          { value: 'OUTCOME_ENGAGEMENT', label: 'User Engagement' },
          { value: 'OUTCOME_AWARENESS', label: 'Brand Awareness' },
          { value: 'OUTCOME_APP_PROMOTION', label: 'App Promotion' },
        ]
        
        this.buyingTypeOptions = [
          { label: 'Auction', value: 'AUCTION' },
          { label: 'Reserved', value: 'RESERVED' }
        ]
        
        this.specialAdCategoryOptions = [
          { label: 'None', value: 'NONE' },
          { label: 'Employment', value: 'EMPLOYMENT' },
          { label: 'Housing', value: 'HOUSING' },
          { label: 'Credit', value: 'CREDIT' },
          { label: 'Social Issues', value: 'SOCIAL_ISSUES' },
          { label: 'Politics', value: 'POLITICS' },
          { label: 'Other', value: 'OTHER' },
        ]
        
        // 实际API调用示例:
        // const [objectives, buyingTypes, specialCategories] = await Promise.all([
        //   getCampaignObjectives(),
        //   getBuyingTypes(),
        //   getSpecialAdCategories()
        // ])
        // this.objectiveOptions = objectives
        // this.buyingTypeOptions = buyingTypes
        // this.specialAdCategoryOptions = specialCategories
      } catch (error) {
        console.error('Failed to get option data', error)
        ElMessage.error('Failed to get option data')
      }
    },
    
    // 获取广告系列详情
    async fetchCampaignDetail(id) {
      this.loading = true
      try {
        // 实际项目中应该从API获取，这里使用模拟数据
        /*const mockData = {
          id: id,
          name: '夏季促销活动 - 电子产品',
          objective: 'TRAFFIC',
          buying_type: 'AUCTION',
          special_ad_categories: ['EMPLOYMENT'],
          daily_budget: 300,
          spend_cap: 5000,
          status: 'ACTIVE',
          start_time: '2023-04-15 10:30:00',
          stop_time: '2023-06-15 10:30:00',
          optimization_goal: 'LINK_CLICKS',
          billing_event: 'LINK_CLICKS'
        }
        
        // 模拟API响应
        Object.assign(this.formData, mockData)
        */
        const res = await getCampaignDetail(id)
        if (res.code === 0) { 
          Object.assign(this.formData, res.data)
        } else {
          ElMessage.error(res.msg)
        }

      } catch (error) {
        console.error('Failed to get campaign details', error)
        ElMessage.error('Failed to get campaign details')
        this.goBack()
      } finally {
        this.loading = false
      }
    },
    
    // 提交表单
    async submitForm() {
      if (!this.formRef) {
        this.formRef = this.$refs.formRef
      }
       
      try {
        await this.formRef.validate()
        
        this.loading = true
        
        if (this.isEdit) {
          const res = await updateCampaign(this.formData.id, this.formData)
          if (res.code === 0) {
            ElMessage.success('Campaign updated successfully')
          } else {
            ElMessage.error(res.msg)
          }
        } else {
          const res = await createCampaign(this.formData)
          if (res.code === 0) {
            ElMessage.success('Campaign created successfully')
          } else {
            ElMessage.error(res.msg)
          }
        }
        
        this.goBack()
      } catch (error) {
        console.error('Form submission failed', error)
        ElMessage.error('Form validation failed, please check your input')
      } finally {
        this.loading = false
      }
    },
    
    // 重置表单
    resetForm() {
      if (this.isEdit) {
        // 编辑模式下重新获取详情
        ElMessageBox.confirm('Are you sure you want to reset the form? This will restore the original data.', 'Tip', {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          this.fetchCampaignDetail(this.$route.params.id)
        }).catch(() => {})
      } else {
        // 创建模式下重置为默认值
        this.formRef?.resetFields()
        Object.assign(this.formData, {
          id: '',
          name: '',
          objective: '',
          buying_type: 'AUCTION',
          special_ad_categories: ['NONE'],
          daily_budget: 0,
          spend_cap: 0,
          status: 'PAUSED',
          start_time: '',
          stop_time: '',
          optimization_goal: '',
          billing_event: 'IMPRESSIONS'
        })
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.push('/campaigns')
    }
  },
  
  mounted() {
    this.fetchOptions()
    
    if (this.isEdit && this.$route.params.id) {
      this.fetchCampaignDetail(this.$route.params.id)
    }
  }
}
</script>

<style lang="scss" scoped>
.campaign-form-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px dashed #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 20px 0;
    padding-left: 10px;
    border-left: 3px solid #409eff;
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 5px;
  }
  
  .budget-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    
    .budget-item {
      flex: 1;
    }
  }
}
</style> 